"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DuplicateDetectionController = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const auth_guard_1 = require("../auth/auth.guard");
class MergeDuplicatesDto {
}
let DuplicateDetectionController = class DuplicateDetectionController {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async findDuplicateVenues() {
        const query = `
      SELECT 
        LOWER(TRIM(name)) as normalized_name,
        COUNT(*) as count,
        json_agg(id::text) as ids,
        string_agg(name, ' | ') as name
      FROM venue
      WHERE deleted IS NOT TRUE
      GROUP BY LOWER(TRIM(name)) 
      HAVING COUNT(*) > 1
      ORDER BY count DESC
    `;
        const results = await this.dataSource.query(query);
        const duplicates = [];
        for (const group of results) {
            const recordMetadata = [];
            for (const id of group.ids) {
                const venueColumns = await this.dataSource.query(`
          SELECT * FROM venue WHERE id = $1 AND deleted IS NOT TRUE LIMIT 1
        `, [id]);
                const hasUpdatedAt = venueColumns.length > 0 && 'updatedAt' in venueColumns[0];
                const hasCreatedAt = venueColumns.length > 0 && 'createdAt' in venueColumns[0];
                const hasImageUrl = venueColumns.length > 0 && 'imageUrl' in venueColumns[0];
                console.log('Venue columns detected:', {
                    hasUpdatedAt,
                    hasCreatedAt,
                    hasImageUrl,
                    sampleKeys: venueColumns.length > 0 ? Object.keys(venueColumns[0]).slice(0, 5) : []
                });
                const updatedAtCol = hasUpdatedAt ? '"updatedAt"' : 'updated_at';
                const createdAtCol = hasCreatedAt ? '"createdAt"' : 'created_at';
                const imageUrlCol = hasImageUrl ? '"imageUrl"' : 'image_url';
                const venueDetails = await this.dataSource.query(`
          SELECT 
            id, 
            name, 
            description, 
            address, 
            city,
            state, 
            website,
            ${imageUrlCol},
            google_place_id,
            COALESCE(${updatedAtCol}, ${createdAtCol}) as updated_at,
            ${createdAtCol} as created_at,
            (SELECT COUNT(*) FROM event WHERE venue_id = v.id) as event_count,
            (CASE 
              WHEN description IS NOT NULL AND description != '' THEN 1 ELSE 0 
            END +
            CASE 
              WHEN address IS NOT NULL AND address != '' THEN 1 ELSE 0 
            END +
            CASE 
              WHEN city IS NOT NULL AND city != '' THEN 1 ELSE 0 
            END +
            CASE 
              WHEN state IS NOT NULL AND state != '' THEN 1 ELSE 0 
            END +
            CASE 
              WHEN website IS NOT NULL AND website != '' THEN 1 ELSE 0 
            END +
            CASE 
              WHEN ${imageUrlCol} IS NOT NULL AND ${imageUrlCol} != '' THEN 1 ELSE 0 
            END) as completeness_score
          FROM venue v
          WHERE id = $1
        `, [id]);
                if (venueDetails.length > 0) {
                    const venue = venueDetails[0];
                    recordMetadata.push({
                        id: venue.id,
                        name: venue.name,
                        eventCount: parseInt(venue.event_count) || 0,
                        completenessScore: parseInt(venue.completeness_score) || 0,
                        updatedAt: venue.updated_at,
                        createdAt: venue.created_at,
                        hasDescription: !!venue.description,
                        hasImage: !!(hasImageUrl ? venue.imageUrl : venue.image_url),
                        hasAddress: !!(venue.address && venue.city),
                        googlePlaceId: venue.google_place_id
                    });
                }
            }
            duplicates.push({
                name: group.name,
                count: parseInt(group.count),
                ids: group.ids,
                recordMetadata
            });
        }
        return duplicates;
    }
    async findDuplicateMusicians() {
        const query = `
      SELECT 
        LOWER(TRIM(name)) as normalized_name,
        COUNT(*) as count,
        json_agg(id::text) as ids,
        string_agg(name, ' | ') as name
      FROM talent
      WHERE deleted IS NOT TRUE
      GROUP BY LOWER(TRIM(name)) 
      HAVING COUNT(*) > 1
      ORDER BY count DESC
    `;
        const results = await this.dataSource.query(query);
        const duplicates = [];
        for (const group of results) {
            const recordMetadata = [];
            for (const id of group.ids) {
                const talentColumns = await this.dataSource.query(`
          SELECT * FROM talent WHERE id = $1 AND deleted IS NOT TRUE LIMIT 1
        `, [id]);
                const hasUpdatedAt = talentColumns.length > 0 && 'updatedAt' in talentColumns[0];
                const hasCreatedAt = talentColumns.length > 0 && 'createdAt' in talentColumns[0];
                const hasImageUrl = talentColumns.length > 0 && 'imageUrl' in talentColumns[0];
                console.log('Talent columns detected:', {
                    hasUpdatedAt,
                    hasCreatedAt,
                    hasImageUrl,
                    sampleKeys: talentColumns.length > 0 ? Object.keys(talentColumns[0]).slice(0, 5) : []
                });
                const updatedAtCol = hasUpdatedAt ? '"updatedAt"' : 'updated_at';
                const createdAtCol = hasCreatedAt ? '"createdAt"' : 'created_at';
                const imageUrlCol = hasImageUrl ? '"imageUrl"' : 'image_url';
                const musicianDetails = await this.dataSource.query(`
          SELECT 
            id, 
            name, 
            bio, 
            genre,
            website,
            ${imageUrlCol},
            COALESCE(${updatedAtCol}, ${createdAtCol}) as updated_at,
            ${createdAtCol} as created_at,
            (SELECT COUNT(*) FROM event_talents WHERE talent_id = t.id) as event_count,
            (CASE 
              WHEN bio IS NOT NULL AND bio != '' THEN 1 ELSE 0 
            END +
            CASE 
              WHEN genre IS NOT NULL AND array_length(genre, 1) > 0 THEN 1 ELSE 0 
            END +
            CASE 
              WHEN website IS NOT NULL AND website != '' THEN 1 ELSE 0 
            END +
            CASE 
              WHEN ${imageUrlCol} IS NOT NULL AND ${imageUrlCol} != '' THEN 1 ELSE 0 
            END) as completeness_score
          FROM talent t
          WHERE id = $1
        `, [id]);
                if (musicianDetails.length > 0) {
                    const musician = musicianDetails[0];
                    recordMetadata.push({
                        id: musician.id,
                        name: musician.name,
                        eventCount: parseInt(musician.event_count) || 0,
                        completenessScore: parseInt(musician.completeness_score) || 0,
                        updatedAt: musician.updated_at,
                        createdAt: musician.created_at,
                        hasDescription: !!musician.bio,
                        hasImage: !!(hasImageUrl ? musician.imageUrl : musician.image_url),
                        hasGenre: !!musician.genre
                    });
                }
            }
            duplicates.push({
                name: group.name,
                count: parseInt(group.count),
                ids: group.ids,
                recordMetadata
            });
        }
        return duplicates;
    }
    async findDuplicateEvents() {
        const query = `
    WITH duplicates AS (
      SELECT 
        name,
        venue_id, 
        "startDateTime" as event_datetime,
        CAST("startDateTime" AS DATE) as event_date,
        COUNT(*) as count,
        json_agg(id::text) as ids
      FROM event
      WHERE deleted IS NOT TRUE
      GROUP BY name, venue_id, "startDateTime"
      HAVING COUNT(*) > 1
      ORDER BY count DESC
    )
    SELECT * FROM duplicates
  `;
        const results = await this.dataSource.query(query);
        const duplicates = [];
        for (const group of results) {
            const recordMetadata = [];
            for (const id of group.ids) {
                const eventDetails = await this.dataSource.query(`
          SELECT 
            e.id, 
            e.name, 
            e.description, 
            e."imageUrl",
            e.venue_id,
            e.azure_id,
            COALESCE(e.updated_at, e.created_at) as updated_at,
            e.created_at as created_at,
            (SELECT COUNT(*) FROM event_talents WHERE event_id = e.id) as talent_count,
            (CASE 
              WHEN e.description IS NOT NULL AND e.description != '' THEN 1 ELSE 0 
            END +
            CASE 
              WHEN e."imageUrl" IS NOT NULL AND e."imageUrl" != '' THEN 1 ELSE 0 
            END) as completeness_score
          FROM event e
          WHERE id = $1
        `, [id]);
                if (eventDetails.length > 0) {
                    const event = eventDetails[0];
                    recordMetadata.push({
                        id: event.id,
                        name: event.name,
                        talentCount: parseInt(event.talent_count) || 0,
                        completenessScore: parseInt(event.completeness_score) || 0,
                        updatedAt: event.updated_at,
                        createdAt: event.created_at,
                        hasDescription: !!event.description,
                        hasImage: !!event.imageUrl,
                        hasTicketInfo: false,
                        venueId: event.venue_id,
                        azureId: event.azure_id
                    });
                }
            }
            duplicates.push({
                name: group.name,
                venue_id: group.venue_id,
                event_date: group.event_date,
                event_datetime: group.event_datetime,
                count: parseInt(group.count),
                ids: group.ids,
                recordMetadata
            });
        }
        return duplicates;
    }
    async getDuplicateCounts() {
        const venueQuery = `
      SELECT COUNT(*) as total_groups
      FROM (
        SELECT 1
        FROM venue 
        WHERE deleted IS NOT TRUE
        GROUP BY LOWER(TRIM(name)) 
        HAVING COUNT(*) > 1
      ) as duplicate_groups
    `;
        const musicianQuery = `
      SELECT COUNT(*) as total_groups
      FROM (
        SELECT 1 
        FROM talent
        WHERE deleted IS NOT TRUE
        GROUP BY LOWER(TRIM(name)) 
        HAVING COUNT(*) > 1
      ) as duplicate_groups
    `;
        const eventQuery = `
    SELECT COUNT(*) as total_groups
    FROM (
      SELECT 1
      FROM event
      WHERE deleted IS NOT TRUE
      GROUP BY name, venue_id, "startDateTime"
      HAVING COUNT(*) > 1
    ) as duplicate_groups
  `;
        const venueCount = await this.dataSource.query(venueQuery);
        const musicianCount = await this.dataSource.query(musicianQuery);
        const eventCount = await this.dataSource.query(eventQuery);
        return {
            venues: parseInt(venueCount[0].total_groups),
            musicians: parseInt(musicianCount[0].total_groups),
            events: parseInt(eventCount[0].total_groups)
        };
    }
    async mergeVenues(mergeDto) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const primaryVenue = await queryRunner.manager.findOne('venue', {
                where: { id: mergeDto.primaryId }
            });
            if (!primaryVenue) {
                throw new Error('Primary venue not found');
            }
            for (const duplicateId of mergeDto.duplicateIds) {
                if (duplicateId === mergeDto.primaryId)
                    continue;
                const duplicateVenue = await queryRunner.manager.findOne('venue', {
                    where: { id: duplicateId }
                });
                if (!duplicateVenue)
                    continue;
                const fieldsToCheck = [
                    'imageUrl', 'photos', 'description', 'address', 'city',
                    'state', 'zipCode', 'phone', 'email', 'website', 'capacity'
                ];
                let updated = false;
                for (const field of fieldsToCheck) {
                    if (!primaryVenue[field] && duplicateVenue[field]) {
                        primaryVenue[field] = duplicateVenue[field];
                        updated = true;
                    }
                }
                if (updated) {
                    await queryRunner.manager.save('venue', primaryVenue);
                }
                await queryRunner.query(`
          UPDATE event 
          SET venue_id = $1 
          WHERE venue_id = $2
        `, [primaryVenue.id, duplicateVenue.id]);
                await queryRunner.query(`
          DELETE FROM venue 
          WHERE id = $1
        `, [duplicateVenue.id]);
            }
            await queryRunner.commitTransaction();
            return { success: true, message: 'Venues merged successfully' };
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async mergeMusicians(mergeDto) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const primaryMusician = await queryRunner.manager.findOne('talent', {
                where: { id: mergeDto.primaryId }
            });
            if (!primaryMusician) {
                throw new Error('Primary musician not found');
            }
            for (const duplicateId of mergeDto.duplicateIds) {
                if (duplicateId === mergeDto.primaryId)
                    continue;
                const duplicateMusician = await queryRunner.manager.findOne('talent', {
                    where: { id: duplicateId }
                });
                if (!duplicateMusician)
                    continue;
                const fieldsToCheck = [
                    'imageUrl', 'photos', 'bio', 'genre',
                    'phone', 'email', 'website', 'socialLinks'
                ];
                let updated = false;
                for (const field of fieldsToCheck) {
                    if (!primaryMusician[field] && duplicateMusician[field]) {
                        primaryMusician[field] = duplicateMusician[field];
                        updated = true;
                    }
                }
                if (updated) {
                    await queryRunner.manager.save('talent', primaryMusician);
                }
                await queryRunner.query(`
          UPDATE event_talents 
          SET talent_id = $1 
          WHERE talent_id = $2
        `, [primaryMusician.id, duplicateMusician.id]);
                await queryRunner.query(`
          DELETE FROM talent 
          WHERE id = $1
        `, [duplicateMusician.id]);
            }
            await queryRunner.commitTransaction();
            return { success: true, message: 'Musicians merged successfully' };
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async batchMergeEvents() {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const duplicateGroups = await this.findDuplicateEvents();
            let mergedGroups = 0;
            let totalMerged = 0;
            for (const group of duplicateGroups) {
                if (group.recordMetadata && group.recordMetadata.length > 1) {
                    const primaryRecord = this.selectPrimaryRecord(group.recordMetadata);
                    const duplicateIds = group.recordMetadata
                        .filter(record => record.id !== primaryRecord.id)
                        .map(record => record.id);
                    if (duplicateIds.length > 0) {
                        await this.mergeEventGroup(queryRunner, primaryRecord.id, duplicateIds);
                        mergedGroups++;
                        totalMerged += duplicateIds.length;
                    }
                }
            }
            await queryRunner.commitTransaction();
            return {
                success: true,
                message: `Batch merge completed: ${mergedGroups} groups merged, ${totalMerged} duplicate events removed`
            };
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    selectPrimaryRecord(records) {
        return records.sort((a, b) => {
            if (b.completenessScore !== a.completenessScore) {
                return b.completenessScore - a.completenessScore;
            }
            if ((b.talentCount || 0) !== (a.talentCount || 0)) {
                return (b.talentCount || 0) - (a.talentCount || 0);
            }
            const aUpdated = new Date(a.updatedAt).getTime();
            const bUpdated = new Date(b.updatedAt).getTime();
            if (bUpdated !== aUpdated) {
                return bUpdated - aUpdated;
            }
            const aCreated = new Date(a.createdAt).getTime();
            const bCreated = new Date(b.createdAt).getTime();
            if (bCreated !== aCreated) {
                return bCreated - aCreated;
            }
            return a.id.localeCompare(b.id);
        })[0];
    }
    async mergeEventGroup(queryRunner, primaryId, duplicateIds) {
        const primaryEvent = await queryRunner.manager.findOne('event', {
            where: { id: primaryId }
        });
        if (!primaryEvent) {
            throw new Error(`Primary event ${primaryId} not found`);
        }
        for (const duplicateId of duplicateIds) {
            const duplicateEvent = await queryRunner.manager.findOne('event', {
                where: { id: duplicateId }
            });
            if (!duplicateEvent)
                continue;
            const fieldsToCheck = ['imageUrl', 'description', 'externalUrl'];
            let updated = false;
            for (const field of fieldsToCheck) {
                if (!primaryEvent[field] && duplicateEvent[field]) {
                    primaryEvent[field] = duplicateEvent[field];
                    updated = true;
                }
            }
            if (updated) {
                await queryRunner.manager.save('event', primaryEvent);
            }
            const duplicateTalents = await queryRunner.query(`
        SELECT talent_id FROM event_talents WHERE event_id = $1
      `, [duplicateEvent.id]);
            const primaryTalents = await queryRunner.query(`
        SELECT talent_id FROM event_talents WHERE event_id = $1
      `, [primaryEvent.id]);
            const primaryTalentIds = primaryTalents.map(t => t.talent_id);
            for (const talent of duplicateTalents) {
                if (!primaryTalentIds.includes(talent.talent_id)) {
                    await queryRunner.query(`
            INSERT INTO event_talents (event_id, talent_id)
            VALUES ($1, $2)
          `, [primaryEvent.id, talent.talent_id]);
                }
            }
            await queryRunner.query(`
        UPDATE event 
        SET deleted = true, updated_at = NOW()
        WHERE id = $1
      `, [duplicateEvent.id]);
        }
        await queryRunner.query(`
      UPDATE event
      SET id_validated = TRUE,
          dedup_validated = TRUE,
          last_validation_date = NOW(),
          validation_version = COALESCE(validation_version, 0) + 1,
          updated_at = NOW()
      WHERE id = $1
    `, [primaryEvent.id]);
    }
    async mergeEvents(mergeDto) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const primaryEvent = await queryRunner.manager.findOne('event', {
                where: { id: mergeDto.primaryId }
            });
            if (!primaryEvent) {
                throw new Error('Primary event not found');
            }
            for (const duplicateId of mergeDto.duplicateIds) {
                if (duplicateId === mergeDto.primaryId)
                    continue;
                const duplicateEvent = await queryRunner.manager.findOne('event', {
                    where: { id: duplicateId }
                });
                if (!duplicateEvent)
                    continue;
                const fieldsToCheck = [
                    'imageUrl', 'description',
                    'externalUrl'
                ];
                let updated = false;
                for (const field of fieldsToCheck) {
                    if (!primaryEvent[field] && duplicateEvent[field]) {
                        primaryEvent[field] = duplicateEvent[field];
                        updated = true;
                    }
                }
                if (updated) {
                    await queryRunner.manager.save('event', primaryEvent);
                }
                await queryRunner.query(`
          UPDATE event
          SET id_validated = TRUE,
              dedup_validated = TRUE,
              last_validation_date = NOW(),
              validation_version = COALESCE(validation_version, 0) + 1,
              updated_at = NOW()
          WHERE id = $1
        `, [primaryEvent.id]);
                const duplicateTalents = await queryRunner.query(`
          SELECT talent_id FROM event_talents WHERE event_id = $1
        `, [duplicateEvent.id]);
                const primaryTalents = await queryRunner.query(`
          SELECT talent_id FROM event_talents WHERE event_id = $1
        `, [primaryEvent.id]);
                const primaryTalentIds = primaryTalents.map(t => t.talent_id);
                for (const talent of duplicateTalents) {
                    if (!primaryTalentIds.includes(talent.talent_id)) {
                        await queryRunner.query(`
              INSERT INTO event_talents (event_id, talent_id)
              VALUES ($1, $2)
            `, [primaryEvent.id, talent.talent_id]);
                    }
                }
                await queryRunner.query(`
          UPDATE event 
          SET deleted = true, updated_at = NOW()
          WHERE id = $1
        `, [duplicateEvent.id]);
            }
            await queryRunner.commitTransaction();
            return { success: true, message: 'Events merged successfully' };
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
};
exports.DuplicateDetectionController = DuplicateDetectionController;
__decorate([
    (0, common_1.Get)('venues'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DuplicateDetectionController.prototype, "findDuplicateVenues", null);
__decorate([
    (0, common_1.Get)('musicians'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DuplicateDetectionController.prototype, "findDuplicateMusicians", null);
__decorate([
    (0, common_1.Get)('events'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DuplicateDetectionController.prototype, "findDuplicateEvents", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DuplicateDetectionController.prototype, "getDuplicateCounts", null);
__decorate([
    (0, common_1.Post)('venues/merge'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [MergeDuplicatesDto]),
    __metadata("design:returntype", Promise)
], DuplicateDetectionController.prototype, "mergeVenues", null);
__decorate([
    (0, common_1.Post)('musicians/merge'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [MergeDuplicatesDto]),
    __metadata("design:returntype", Promise)
], DuplicateDetectionController.prototype, "mergeMusicians", null);
__decorate([
    (0, common_1.Post)('events/batch-merge'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DuplicateDetectionController.prototype, "batchMergeEvents", null);
__decorate([
    (0, common_1.Post)('events/merge'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [MergeDuplicatesDto]),
    __metadata("design:returntype", Promise)
], DuplicateDetectionController.prototype, "mergeEvents", null);
exports.DuplicateDetectionController = DuplicateDetectionController = __decorate([
    (0, common_1.Controller)('admin/duplicates'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, typeorm_1.InjectDataSource)()),
    __metadata("design:paramtypes", [typeorm_2.DataSource])
], DuplicateDetectionController);
//# sourceMappingURL=duplicate-detection.controller.js.map