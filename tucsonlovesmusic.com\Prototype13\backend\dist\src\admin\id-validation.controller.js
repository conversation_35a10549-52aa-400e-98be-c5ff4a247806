"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var IdValidationController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.IdValidationController = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const auth_guard_1 = require("../auth/auth.guard");
const column_detector_1 = require("../utils/column-detector");
let IdValidationController = IdValidationController_1 = class IdValidationController {
    constructor(dataSource) {
        this.dataSource = dataSource;
        this.logger = new common_1.Logger(IdValidationController_1.name);
        this.columnDetector = new column_detector_1.ColumnDetector(dataSource);
    }
    combineDateTime(dateStr, timeStr) {
        if (!dateStr)
            return null;
        const cleanDateStr = dateStr.includes(' ') ? dateStr.split(' ')[0] : dateStr;
        const cleanTimeStr = timeStr || '00:00:00';
        return `${cleanDateStr} ${cleanTimeStr}`;
    }
    async checkAzureDbConnection() {
        const sql = require('mssql');
        try {
            const config = {
                server: 'mssql.drv1.umbhost.net',
                database: 'TLM',
                user: 'Reader',
                password: 'TLM1234!',
                options: {
                    encrypt: false,
                    trustServerCertificate: true
                }
            };
            const pool = await sql.connect(config);
            this.logger.log('Successfully connected to Azure SQL');
            return pool;
        }
        catch (error) {
            this.logger.error(`Failed to connect to Azure SQL: ${error.message}`);
            throw error;
        }
    }
    async getEventIssues() {
        try {
            const issues = await this.getMissingEvents();
            this.logger.log(`Returning ${issues.length} event validation issues`);
            return issues;
        }
        catch (error) {
            this.logger.error(`Error fetching event validation issues: ${error.message}`);
            throw new common_1.InternalServerErrorException(`Error fetching event validation issues: ${error.message}`);
        }
    }
    processEventDates(azureEvent) {
        this.logger.log(`Processing dates for event: ${azureEvent.Name || 'Unknown'} (${azureEvent.Id})`);
        let startDateTime, endDateTime;
        if (azureEvent.EventStartDateStr) {
            const startDateStr = azureEvent.EventStartDateStr;
            let startTimeStr = '12:00:00';
            if (azureEvent.EventStartTimeStr) {
                startTimeStr = azureEvent.EventStartTimeStr;
                this.logger.log(`Using string time directly: ${startTimeStr}`);
            }
            else if (azureEvent.EventStartTime) {
                if (typeof azureEvent.EventStartTime === 'string') {
                    const timeMatch = azureEvent.EventStartTime.match(/^(\d{2}):(\d{2}):(\d{2})/);
                    if (timeMatch) {
                        startTimeStr = `${timeMatch[1]}:${timeMatch[2]}:${timeMatch[3]}`;
                        this.logger.log(`Extracted start time ${startTimeStr} using regex`);
                    }
                    else {
                        startTimeStr = azureEvent.EventStartTime.slice(0, 8);
                        this.logger.log(`Fallback start time extraction: ${startTimeStr}`);
                    }
                }
                else if (azureEvent.EventStartTime instanceof Date) {
                    const hours = azureEvent.EventStartTime.getHours();
                    const minutes = azureEvent.EventStartTime.getMinutes();
                    const seconds = azureEvent.EventStartTime.getSeconds();
                    startTimeStr = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
                    this.logger.log(`Extracted start time ${startTimeStr} from Date object`);
                }
            }
            startDateTime = `${startDateStr} ${startTimeStr}`;
            this.logger.log(`Constructed start datetime: ${startDateTime}`);
        }
        else if (azureEvent.EventStartDate) {
            this.logger.log('WARNING: Using Date object fallback for start date');
            const year = azureEvent.EventStartDate.getFullYear();
            const month = String(azureEvent.EventStartDate.getMonth() + 1).padStart(2, '0');
            const day = String(azureEvent.EventStartDate.getDate()).padStart(2, '0');
            startDateTime = `${year}-${month}-${day} 12:00:00`;
        }
        else {
            this.logger.log('WARNING: No start date found, using current date');
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            startDateTime = `${year}-${month}-${day} 12:00:00`;
        }
        if (azureEvent.EventEndDateStr) {
            const endDateStr = azureEvent.EventEndDateStr;
            let endTimeStr = '23:59:00';
            if (azureEvent.EventEndTimeStr) {
                endTimeStr = azureEvent.EventEndTimeStr;
                this.logger.log(`Using string end time directly: ${endTimeStr}`);
            }
            else if (azureEvent.EventEndTime) {
                if (typeof azureEvent.EventEndTime === 'string') {
                    const timeMatch = azureEvent.EventEndTime.match(/^(\d{2}):(\d{2}):(\d{2})/);
                    if (timeMatch) {
                        endTimeStr = `${timeMatch[1]}:${timeMatch[2]}:${timeMatch[3]}`;
                        this.logger.log(`Extracted end time ${endTimeStr} using regex`);
                    }
                    else {
                        endTimeStr = azureEvent.EventEndTime.slice(0, 8);
                        this.logger.log(`Fallback end time extraction: ${endTimeStr}`);
                    }
                }
                else if (azureEvent.EventEndTime instanceof Date) {
                    const hours = azureEvent.EventEndTime.getHours();
                    const minutes = azureEvent.EventEndTime.getMinutes();
                    const seconds = azureEvent.EventEndTime.getSeconds();
                    endTimeStr = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
                    this.logger.log(`Extracted end time ${endTimeStr} from Date object`);
                }
            }
            endDateTime = `${endDateStr} ${endTimeStr}`;
            this.logger.log(`Constructed end datetime: ${endDateTime}`);
        }
        else {
            const startDateParts = startDateTime.split(' ')[0];
            endDateTime = `${startDateParts} 23:59:00`;
            this.logger.log(`No end date found, using start date with end of day: ${endDateTime}`);
        }
        return { startDateTime, endDateTime };
    }
    async batchFixEvents() {
        this.logger.log('Starting batch fix for missing events');
        try {
            const issues = await this.getMissingEvents();
            const missingPostgresIssues = issues.filter(issue => issue.issueType === 'missing_postgres');
            this.logger.log(`Found ${missingPostgresIssues.length} events missing in PostgreSQL to fix`);
            const result = {
                totalIssues: missingPostgresIssues.length,
                processedCount: 0,
                successCount: 0,
                errorCount: 0,
                errors: [],
                fixedEvents: []
            };
            const sql = require('mssql');
            const pool = await this.checkAzureDbConnection();
            try {
                for (const issue of missingPostgresIssues) {
                    let queryRunner = null;
                    try {
                        result.processedCount++;
                        if (!issue.azureRecord || !issue.azureRecord.id) {
                            throw new Error(`Missing Azure ID for event: ${issue.description}`);
                        }
                        const azureId = issue.azureRecord.id;
                        const eventQuery = `
              SELECT 
                Id, Name, Description, VenueId, EventStartDate, EventEndDate, 
                EventStartTime, EventEndTime, CreatedOn,
                CONVERT(varchar, EventStartDate, 23) AS EventStartDateStr,
                CONVERT(varchar, EventStartTime, 8) AS EventStartTimeStr,
                CONVERT(varchar, EventEndDate, 23) AS EventEndDateStr,
                CONVERT(varchar, EventEndTime, 8) AS EventEndTimeStr
              FROM PerformanceEvents 
              WHERE Id = @azureId
            `;
                        const request = pool.request();
                        request.input('azureId', sql.UniqueIdentifier, azureId);
                        const eventResult = await request.query(eventQuery);
                        if (!eventResult.recordset || eventResult.recordset.length === 0) {
                            throw new Error(`Event not found in MSSQL: ${azureId}`);
                        }
                        const azureEvent = eventResult.recordset[0];
                        queryRunner = this.dataSource.createQueryRunner();
                        await queryRunner.connect();
                        await queryRunner.startTransaction();
                        try {
                            const { startDateTime, endDateTime } = this.processEventDates(azureEvent);
                            this.logger.log(`Processed dates: Start=${startDateTime}, End=${endDateTime}`);
                            if (!startDateTime) {
                                this.logger.warn(`No valid start date could be processed, skipping event ${azureEvent.Id}`);
                                throw new Error('Missing required start date');
                            }
                            let venueIds = [];
                            if (azureEvent.VenueId && azureEvent.VenueId !== '' && azureEvent.VenueId !== 'null') {
                                try {
                                    venueIds = await queryRunner.query(`
                    SELECT DISTINCT id
                    FROM venue
                    WHERE 
                      deleted IS NOT TRUE
                      AND (
                        azure_id::text = $1
                        OR LOWER(TRIM(name)) IN (
                          SELECT LOWER(TRIM(name))
                          FROM venue
                          WHERE azure_id::text = $1 AND deleted IS NOT TRUE
                        )
                      )
                  `, [azureEvent.VenueId]);
                                }
                                catch (error) {
                                    this.logger.warn(`Error in venue lookup for ${azureEvent.VenueId}: ${error.message}, using fallback`);
                                    venueIds = await queryRunner.query(`
                    SELECT DISTINCT id FROM venue WHERE deleted IS NOT TRUE
                  `);
                                }
                            }
                            else {
                                venueIds = await queryRunner.query(`
                  SELECT DISTINCT id FROM venue WHERE deleted IS NOT TRUE
                `);
                            }
                            const venueIdList = venueIds.map(v => v.id);
                            const existingEventCheck = await queryRunner.query(`
                SELECT id, name, azure_id, "startDateTime", venue_id
                FROM event
                WHERE 
                  deleted IS NOT TRUE
                  AND name = $1
                  AND venue_id = ANY($2::uuid[])
                  AND "startDateTime" = $3
              `, [azureEvent.Name, venueIdList, startDateTime]);
                            if (existingEventCheck.length > 0) {
                                const duplicate = existingEventCheck[0];
                                this.logger.log(`Found potential duplicate for Azure event ${azureEvent.Id} (${azureEvent.Name}): ${duplicate.id} (${duplicate.name})`);
                                if (!duplicate.azure_id) {
                                    await queryRunner.query(`
                    UPDATE event
                    SET 
                      azure_id = $1,
                      updated_at = NOW(),
                      id_validated = TRUE,
                      dedup_validated = TRUE, 
                      last_validation_date = NOW(),
                      validation_version = COALESCE(validation_version, 0) + 1
                    WHERE id = $2
                  `, [azureEvent.Id, duplicate.id]);
                                    result.successCount++;
                                    result.fixedEvents.push({
                                        id: duplicate.id,
                                        name: duplicate.name,
                                        azureId: azureEvent.Id,
                                        message: 'Updated existing event with Azure ID'
                                    });
                                    this.logger.log(`Updated existing event ${duplicate.id} (${duplicate.name}) with Azure ID ${azureEvent.Id}`);
                                    await queryRunner.commitTransaction();
                                    continue;
                                }
                            }
                            const insertResult = await queryRunner.query(`
                INSERT INTO event (
                  id, azure_id, name, description, "startDateTime", "endDateTime", venue_id, 
                  created_at, updated_at, deleted, id_validated, dedup_validated, last_validation_date, validation_version
                ) VALUES (
                  uuid_generate_v4(), $1, $2, $3, $4, $5, 
                  (SELECT id FROM venue WHERE azure_id::text = $6 LIMIT 1),
                  NOW(), NOW(), FALSE, TRUE, TRUE, NOW(), 1
                ) RETURNING id
              `, [
                                azureEvent.Id,
                                azureEvent.Name,
                                azureEvent.Description || '',
                                startDateTime,
                                endDateTime,
                                azureEvent.VenueId
                            ]);
                            await queryRunner.commitTransaction();
                            result.successCount++;
                            result.fixedEvents.push({
                                id: insertResult[0].id,
                                name: azureEvent.Name,
                                azureId: azureEvent.Id
                            });
                            this.logger.log(`Successfully created event in PostgreSQL: ${azureEvent.Name} (${azureEvent.Id})`);
                        }
                        catch (transactionError) {
                            try {
                                if (queryRunner) {
                                    await queryRunner.rollbackTransaction();
                                }
                            }
                            catch (rollbackError) {
                                this.logger.error(`Error during rollback: ${rollbackError.message}`);
                            }
                            throw transactionError;
                        }
                        finally {
                            try {
                                if (queryRunner) {
                                    await queryRunner.release();
                                }
                            }
                            catch (releaseError) {
                                this.logger.error(`Error releasing query runner: ${releaseError.message}`);
                            }
                        }
                    }
                    catch (eventError) {
                        result.errorCount++;
                        result.errors.push({
                            event: issue.azureRecord?.name || 'Unknown',
                            error: eventError.message
                        });
                        this.logger.error(`Error processing event ${issue.azureRecord?.id}: ${eventError.message}`);
                        this.logger.error(`Error stack: ${eventError.stack}`);
                        if (queryRunner) {
                            try {
                                if (queryRunner.isTransactionActive) {
                                    await queryRunner.rollbackTransaction();
                                }
                                if (!queryRunner.isReleased) {
                                    await queryRunner.release();
                                }
                            }
                            catch (cleanupError) {
                                this.logger.error(`Error during cleanup: ${cleanupError.message}`);
                            }
                        }
                    }
                }
            }
            finally {
                if (pool) {
                    try {
                        await pool.close();
                        this.logger.log('MSSQL connection closed');
                    }
                    catch (closeError) {
                        this.logger.error(`Error closing MSSQL connection: ${closeError.message}`);
                    }
                }
            }
            this.logger.log(`Batch fix completed: ${result.successCount} events fixed, ${result.errorCount} errors`);
            return result;
        }
        catch (error) {
            this.logger.error(`Error in batch fix: ${error.message}`);
            throw new common_1.InternalServerErrorException(`Error in batch fix: ${error.message}`);
        }
    }
    async getValidationCounts() {
        try {
            await this.checkAzureDbConnection();
            const venuesMissingAzureQuery = `
        SELECT COUNT(*) as count 
        FROM venue 
        WHERE deleted = FALSE
        AND (azure_id IS NULL OR azure_id = '')
      `;
            const musiciansMissingAzureQuery = `
        SELECT COUNT(*) as count 
        FROM talent 
        WHERE deleted = FALSE
        AND (azure_id IS NULL OR azure_id = '')
      `;
            const [venueResult, musicianResult] = await Promise.all([
                this.dataSource.query(venuesMissingAzureQuery),
                this.dataSource.query(musiciansMissingAzureQuery),
            ]);
            let eventCount = 0;
            try {
                const issues = await this.getMissingEvents();
                eventCount = issues.length;
                this.logger.log(`Found exactly ${issues.length} event validation issues using getMissingEvents()`);
                issues.forEach(issue => {
                    this.logger.log(`- Issue: ${issue.description}`);
                });
            }
            catch (missingError) {
                this.logger.error(`Error counting missing events: ${missingError.message}`);
            }
            return {
                venueCount: venueResult[0].count,
                musicianCount: musicianResult[0].count,
                eventCount: eventCount
            };
        }
        catch (error) {
            this.logger.error(`Error fetching validation counts: ${error.message}`);
            throw new common_1.InternalServerErrorException(`Error fetching validation counts: ${error.message}`);
        }
    }
    async getAzureRecordById(entityType, azureId) {
        const sql = require('mssql');
        let pool = null;
        try {
            this.logger.log(`Fetching Azure ${entityType} record with ID: ${azureId}`);
            if (!azureId || azureId.trim() === '') {
                this.logger.error('Invalid Azure ID: Empty or undefined');
                throw new common_1.NotFoundException('Invalid Azure ID: Empty or undefined');
            }
            const cleanAzureId = azureId.trim();
            this.logger.log(`Cleaned Azure ID format: ${cleanAzureId}`);
            const isValidFormat = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}(?:-[0-9a-f]+)*$/i.test(cleanAzureId);
            this.logger.log(`Azure ID format validation: ${isValidFormat ? 'VALID' : 'INVALID'} - ${cleanAzureId}`);
            if (!isValidFormat) {
                this.logger.error(`Invalid Azure ID format: ${cleanAzureId}`);
                throw new common_1.NotFoundException(`Invalid Azure ID format: ${cleanAzureId}`);
            }
            try {
                pool = await this.checkAzureDbConnection();
                if (entityType === 'events') {
                    const result = await pool.request()
                        .input('eventId', sql.UniqueIdentifier, cleanAzureId)
                        .query(`
              SELECT 
                Id, 
                Name, 
                Description,
                EventStartDate,
                EventStartTime,
                EventEndDate,
                EventEndTime,
                CONVERT(varchar, EventStartDate, 23) AS EventStartDateStr,
                CONVERT(varchar, EventStartTime, 8) AS EventStartTimeStr,
                CONVERT(varchar, EventEndDate, 23) AS EventEndDateStr,
                CONVERT(varchar, EventEndTime, 8) AS EventEndTimeStr,
                VenueId,
                FeatureLevel,
                ProfileImageCropperValue,
                ContactName,
                ContactEmailAddress,
                ContactPhoneNumber,
                ContactWebsite,
                Locale,
                Url,
                CreatedOn,
                UpdatedOn
              FROM PerformanceEvents 
              WHERE Id = @eventId
            `);
                    if (result.recordset.length === 0) {
                        throw new common_1.NotFoundException(`Event with ID ${cleanAzureId} not found in Azure`);
                    }
                    const azureEvent = result.recordset[0];
                    let startDateTime = null;
                    let endDateTime = null;
                    try {
                        if (azureEvent.EventStartDateStr) {
                            const startDateStr = azureEvent.EventStartDateStr;
                            let startTimeStr = '00:00:00';
                            if (azureEvent.EventStartTimeStr) {
                                startTimeStr = azureEvent.EventStartTimeStr;
                            }
                            const startDateTimeStr = `${startDateStr}T${startTimeStr}`;
                            startDateTime = new Date(startDateTimeStr);
                            this.logger.log(`Processed start date/time: ${startDateTimeStr}`);
                        }
                        if (azureEvent.EventEndDateStr) {
                            const endDateStr = azureEvent.EventEndDateStr;
                            let endTimeStr = '00:00:00';
                            if (azureEvent.EventEndTimeStr) {
                                endTimeStr = azureEvent.EventEndTimeStr;
                            }
                            const endDateTimeStr = `${endDateStr}T${endTimeStr}`;
                            endDateTime = new Date(endDateTimeStr);
                            this.logger.log(`Processed end date/time: ${endDateTimeStr}`);
                        }
                        else if (azureEvent.EventStartDateStr && azureEvent.EventEndTimeStr) {
                            const endDateStr = azureEvent.EventStartDateStr;
                            const endTimeStr = azureEvent.EventEndTimeStr;
                            const endDateTimeStr = `${endDateStr}T${endTimeStr}`;
                            endDateTime = new Date(endDateTimeStr);
                        }
                    }
                    catch (error) {
                        this.logger.error(`Error processing event date/time: ${error.message}`);
                    }
                    let imageUrl = '';
                    if (azureEvent.ProfileImageCropperValue && typeof azureEvent.ProfileImageCropperValue === 'string') {
                        try {
                            const imageData = JSON.parse(azureEvent.ProfileImageCropperValue);
                            if (imageData && imageData.Src) {
                                const imagePath = imageData.Src;
                                imageUrl = imagePath.startsWith('http')
                                    ? imagePath
                                    : `https://tucsonlovesmusic.com${imagePath}`;
                            }
                        }
                        catch (e) {
                            this.logger.error(`Error parsing event ProfileImageCropperValue JSON: ${e.message}`);
                        }
                    }
                    return {
                        id: azureEvent.Id,
                        name: azureEvent.Name || 'Unknown Event',
                        description: azureEvent.Description || '',
                        startDateTime: azureEvent.EventStartDateStr ? `${azureEvent.EventStartDateStr} ${azureEvent.EventStartTimeStr || '00:00:00'}` : null,
                        endDateTime: azureEvent.EventEndDateStr ? `${azureEvent.EventEndDateStr} ${azureEvent.EventEndTimeStr || '00:00:00'}` : null,
                        venueId: azureEvent.VenueId,
                        imageUrl: imageUrl,
                        featured: azureEvent.FeatureLevel > 0,
                        url: azureEvent.Url
                    };
                }
                else if (entityType === 'venues') {
                    const result = await pool.request()
                        .input('venueId', sql.UniqueIdentifier, cleanAzureId)
                        .query(`
              SELECT 
                Id, 
                Name, 
                ContactWebsite as Website, 
                MapLocation, 
                Locale,
                CreatedOn,
                UpdatedOn
              FROM Venues 
              WHERE Id = @venueId
            `);
                    if (result.recordset.length === 0) {
                        throw new common_1.NotFoundException(`Venue with ID ${cleanAzureId} not found in Azure`);
                    }
                    const venue = result.recordset[0];
                    let locale = null;
                    let address = '';
                    let city = '';
                    let state = '';
                    let zipCode = '';
                    let googlePlaceId = '';
                    let latitude = null;
                    let longitude = null;
                    if (venue.Locale) {
                        try {
                            if (typeof venue.Locale === 'string') {
                                locale = JSON.parse(venue.Locale);
                            }
                            else {
                                locale = venue.Locale;
                            }
                            if (locale?.MapLocation?.address) {
                                const localeAddress = locale.MapLocation.address;
                                if (localeAddress.full_address) {
                                    address = localeAddress.full_address;
                                    this.logger.log(`Found address in Locale: ${address}`);
                                }
                                if (localeAddress.city) {
                                    city = localeAddress.city;
                                }
                                if (localeAddress.state) {
                                    state = localeAddress.state;
                                }
                                if (localeAddress.postalcode) {
                                    zipCode = localeAddress.postalcode;
                                }
                                if (localeAddress.coordinates && !localeAddress.coordinates.IsEmpty) {
                                    latitude = localeAddress.coordinates.lat;
                                    longitude = localeAddress.coordinates.lng;
                                }
                            }
                        }
                        catch (error) {
                            this.logger.warn(`Failed to parse Locale: ${error.message}`);
                        }
                    }
                    if (!address && venue.MapLocation) {
                        try {
                            let mapLocationObj;
                            if (typeof venue.MapLocation === 'string') {
                                mapLocationObj = JSON.parse(venue.MapLocation);
                            }
                            else {
                                mapLocationObj = venue.MapLocation;
                            }
                            if (mapLocationObj.address) {
                                if (mapLocationObj.address.full_address) {
                                    address = mapLocationObj.address.full_address;
                                    this.logger.log(`Found address in MapLocation: ${address}`);
                                }
                                if (!city && mapLocationObj.address.city) {
                                    city = mapLocationObj.address.city;
                                }
                                if (!state && mapLocationObj.address.state) {
                                    state = mapLocationObj.address.state;
                                }
                                if (!zipCode && mapLocationObj.address.postalcode) {
                                    zipCode = mapLocationObj.address.postalcode;
                                }
                                if (!latitude && !longitude && mapLocationObj.address.coordinates && !mapLocationObj.address.coordinates.IsEmpty) {
                                    latitude = mapLocationObj.address.coordinates.lat;
                                    longitude = mapLocationObj.address.coordinates.lng;
                                }
                            }
                        }
                        catch (error) {
                            this.logger.warn(`Failed to parse MapLocation: ${error.message}`);
                        }
                    }
                    if (!address && venue.Address) {
                        address = venue.Address;
                    }
                    if (!city && venue.City) {
                        city = venue.City;
                    }
                    if (!state && venue.State) {
                        state = venue.State;
                    }
                    return {
                        Id: venue.Id,
                        Name: venue.Name || 'Unknown Venue',
                        Address: address,
                        City: city || '',
                        State: state || '',
                        Zip: zipCode || '',
                        Website: venue.Website || '',
                        GooglePlaceId: googlePlaceId || '',
                        Latitude: latitude,
                        Longitude: longitude,
                        CreatedAt: venue.CreatedOn ? new Date(venue.CreatedOn).toISOString() : new Date().toISOString(),
                        UpdatedAt: venue.UpdatedOn ? new Date(venue.UpdatedOn).toISOString() : new Date().toISOString(),
                        Locale: locale
                    };
                }
                else if (entityType === 'musicians') {
                    const result = await pool.request()
                        .input('talentId', sql.UniqueIdentifier, cleanAzureId)
                        .query(`
              SELECT 
                Id, 
                Name, 
                Description as Bio,
                FeatureLevel as Category,
                ContactName,
                ContactEmailAddress as Email,
                ContactPhoneNumber as PhoneNumber,
                ContactWebsite as Website,
                ProfileImageCropperValue,
                ProfileImageId,
                Url,
                CreatedOn as CreatedAt,
                UpdatedOn as UpdatedAt
              FROM Talent 
              WHERE Id = @talentId
            `);
                    if (result.recordset.length === 0) {
                        throw new common_1.NotFoundException(`Musician with ID ${cleanAzureId} not found in Azure`);
                    }
                    const musician = result.recordset[0];
                    let imageUrl = '';
                    if (musician.ProfileImageCropperValue && typeof musician.ProfileImageCropperValue === 'string') {
                        try {
                            const imageData = JSON.parse(musician.ProfileImageCropperValue);
                            this.logger.log('Parsed ProfileImageCropperValue successfully');
                            if (imageData && imageData.Src) {
                                const imagePath = imageData.Src;
                                this.logger.log(`Found image path: ${imagePath}`);
                                imageUrl = imagePath.startsWith('http')
                                    ? imagePath
                                    : `https://tucsonlovesmusic.com${imagePath}`;
                                musician.ImageData = imageData;
                            }
                        }
                        catch (e) {
                            this.logger.error(`Error parsing musician ProfileImageCropperValue JSON: ${e.message}`);
                        }
                    }
                    const genre = 'Unknown';
                    return {
                        Id: musician.Id,
                        Name: musician.Name || 'Unknown Musician',
                        Bio: musician.Bio || '',
                        Category: musician.Category || '',
                        Genre: genre,
                        ContactName: musician.ContactName || '',
                        Email: musician.Email || '',
                        PhoneNumber: musician.PhoneNumber || '',
                        Website: musician.Website || '',
                        ImageUrl: imageUrl,
                        Url: musician.Url || '',
                        CreatedAt: musician.CreatedAt ? new Date(musician.CreatedAt).toISOString() : new Date().toISOString(),
                        UpdatedAt: musician.UpdatedAt ? new Date(musician.UpdatedAt).toISOString() : new Date().toISOString(),
                        ImageData: musician.ImageData || null,
                        ProfileImageId: musician.ProfileImageId || null
                    };
                }
                else if (entityType === 'events') {
                    const result = await pool.request()
                        .input('eventId', sql.UniqueIdentifier, cleanAzureId)
                        .query(`
              SELECT 
                Id, 
                Name, 
                Description, 
                EventStartDate,
                EventStartTime,
                EventEndDate,
                EventEndTime,
                CONVERT(varchar, EventStartDate, 23) AS EventStartDateStr,
                CONVERT(varchar, EventStartTime, 8) AS EventStartTimeStr,
                CONVERT(varchar, EventEndDate, 23) AS EventEndDateStr,
                CONVERT(varchar, EventEndTime, 8) AS EventEndTimeStr,
                VenueId,
                FeatureLevel,
                ProfileImageId,
                ProfileImageCropperValue,
                ContactName,
                ContactEmailAddress,
                ContactPhoneNumber,
                ContactWebsite,
                CreatedOn,
                UpdatedOn
              FROM PerformanceEvents 
              WHERE Id = @eventId
            `);
                    if (result.recordset.length === 0) {
                        throw new common_1.NotFoundException(`Event with ID ${cleanAzureId} not found in Azure`);
                    }
                    const event = result.recordset[0];
                    const startDateTime = this.combineDateTime(event.EventStartDateStr, event.EventStartTimeStr);
                    const endDateTime = this.combineDateTime(event.EventEndDateStr, event.EventEndTimeStr);
                    let imageUrl = '';
                    let cropData = null;
                    if (event.ProfileImageCropperValue && typeof event.ProfileImageCropperValue === 'string') {
                        try {
                            const imageData = JSON.parse(event.ProfileImageCropperValue);
                            if (imageData && imageData.Src) {
                                const imagePath = imageData.Src;
                                imageUrl = imagePath.startsWith('http')
                                    ? imagePath
                                    : `https://tucsonlovesmusic.com${imagePath}`;
                                cropData = imageData;
                            }
                        }
                        catch (e) {
                            this.logger.error(`Error parsing event ProfileImageCropperValue JSON: ${e.message}`);
                        }
                    }
                    return {
                        Id: event.Id,
                        Name: event.Name || 'Unknown Event',
                        Description: event.Description || '',
                        StartDateTime: startDateTime,
                        EndDateTime: endDateTime,
                        VenueId: event.VenueId,
                        ContactName: event.ContactName || '',
                        ContactEmail: event.ContactEmailAddress || '',
                        ContactPhone: event.ContactPhoneNumber || '',
                        ContactWebsite: event.ContactWebsite || '',
                        FeatureLevel: event.FeatureLevel || 0,
                        ImageUrl: imageUrl,
                        ProfileImageId: event.ProfileImageId,
                        CropData: cropData,
                        CreatedAt: event.CreatedOn ? new Date(event.CreatedOn).toISOString() : new Date().toISOString(),
                        UpdatedAt: event.UpdatedOn ? new Date(event.UpdatedOn).toISOString() : new Date().toISOString()
                    };
                }
                throw new common_1.NotFoundException(`Unsupported entity type: ${entityType}`);
            }
            finally {
                if (pool) {
                    await pool.close();
                }
            }
        }
        catch (error) {
            this.logger.error(`Error fetching Azure record: ${error.message}`);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Failed to fetch Azure record: ${error.message}`);
        }
    }
    async getValidationIssues(entityType) {
        if (!['venues', 'musicians', 'events', 'missing_events'].includes(entityType)) {
            throw new common_1.NotFoundException(`Invalid entity type: ${entityType}`);
        }
        try {
            const issues = [];
            const azureDbAvailable = await this.checkAzureDbConnection();
            if (entityType === 'venues') {
                const venuesMissingAzure = await this.dataSource.query(`
          SELECT id, name, address, city, state, image_url, created_at, updated_at, website
          FROM venue
          WHERE (azure_id IS NULL OR azure_id = '')
          AND deleted IS NOT TRUE
          LIMIT 100
        `);
                venuesMissingAzure.forEach(venue => {
                    issues.push({
                        id: venue.id,
                        entityType: 'venues',
                        postgresRecord: venue,
                        azureRecord: null,
                        issueType: 'missing_azure',
                        description: `Venue "${venue.name}" exists in PostgreSQL but has no Azure ID`,
                        createdAt: new Date().toISOString()
                    });
                });
                if (azureDbAvailable) {
                    this.logger.log('Checking for Azure venues not in PostgreSQL');
                }
            }
            else if (entityType === 'musicians') {
                try {
                    const musiciansMissingAzure = await this.dataSource.query(`
            SELECT id, name, bio, category, genre, "imageUrl", "createdAt", "updatedAt", website, email, "phoneNumber", "socialMedia"
            FROM talent
            WHERE (azure_id IS NULL OR azure_id = '')
            AND deleted IS NOT TRUE
            LIMIT 100
          `);
                    musiciansMissingAzure.forEach(musician => {
                        issues.push({
                            id: musician.id,
                            entityType: 'musicians',
                            postgresRecord: {
                                id: musician.id,
                                name: musician.name,
                                bio: musician.bio,
                                category: musician.category,
                                genre: musician.genre,
                                imageUrl: musician.imageUrl,
                                email: musician.email,
                                phoneNumber: musician.phoneNumber,
                                website: musician.website,
                                socialMedia: musician.socialMedia,
                                createdAt: musician.createdAt,
                                updatedAt: musician.updatedAt
                            },
                            azureRecord: null,
                            issueType: 'missing_azure',
                            description: `Musician "${musician.name}" exists in PostgreSQL but has no Azure ID`,
                            createdAt: new Date().toISOString()
                        });
                    });
                    if (azureDbAvailable) {
                        this.logger.log('Checking for Azure musicians not in PostgreSQL');
                    }
                }
                catch (musicianError) {
                    this.logger.error(`Error fetching musicians issues: ${musicianError.message}`);
                    throw new common_1.InternalServerErrorException(`Error fetching musicians issues: ${musicianError.message}`);
                }
            }
            else if (entityType === 'events') {
                try {
                    const eventsMissingAzure = await this.dataSource.query(`
            SELECT e.id, e.name, e."imageUrl", 
                  e."startDateTime", e."endDateTime",
                  e.created_at, e.updated_at, 
                  v.name as venue_name, v.id as venue_id
            FROM event e
            LEFT JOIN venue v ON e.venue_id = v.id
            WHERE e.deleted = FALSE
            AND (e.azure_id IS NULL OR e.azure_id = '')
            LIMIT 100
          `);
                    eventsMissingAzure.forEach(event => {
                        issues.push({
                            id: event.id,
                            entityType: 'events',
                            postgresRecord: event,
                            azureRecord: null,
                            issueType: 'missing_azure',
                            description: `Event "${event.name}" at ${event.venue_name || 'Unknown Venue'} exists in PostgreSQL but has no Azure ID`,
                            createdAt: new Date().toISOString()
                        });
                    });
                    try {
                        const missingEvents = await this.getMissingEvents();
                        this.logger.log(`Found ${missingEvents.length} events in MSSQL that are missing from PostgreSQL`);
                        issues.push(...missingEvents);
                    }
                    catch (missingError) {
                        this.logger.error(`Error fetching missing events: ${missingError.message}`);
                    }
                }
                catch (eventError) {
                    this.logger.error(`Error fetching events issues: ${eventError.message}`);
                    throw new common_1.InternalServerErrorException(`Error fetching events issues: ${eventError.message}`);
                }
            }
            else if (entityType === 'missing_events') {
                const missingEvents = await this.getMissingEvents();
                issues.push(...missingEvents);
            }
            return issues;
        }
        catch (error) {
            this.logger.error(`Error fetching ${entityType} issues: ${error.message}`);
            throw new common_1.InternalServerErrorException(`Failed to fetch ${entityType} issues: ${error.message}`);
        }
    }
    async updateAzureId(data) {
        this.logger.log(`Updating ${data.tableName} record ${data.recordId} with Azure ID ${data.azureId}`);
        try {
            const validTables = ['venue', 'venues', 'talent', 'talents', 'event', 'events', 'musicians'];
            if (!validTables.includes(data.tableName)) {
                throw new common_1.BadRequestException(`Invalid table name: ${data.tableName}`);
            }
            let normalizedTable;
            let updatedAtColumn;
            if (data.tableName === 'musicians' || data.tableName === 'talent' || data.tableName === 'talents') {
                normalizedTable = 'talent';
                updatedAtColumn = '"updatedAt"';
            }
            else {
                normalizedTable = data.tableName.replace(/s$/, '');
                updatedAtColumn = 'updated_at';
            }
            const updateQuery = `
        UPDATE ${normalizedTable}
        SET azure_id = $1, ${updatedAtColumn} = $2
        WHERE id = $3
        RETURNING id, name, azure_id
      `;
            const updateResult = await this.dataSource.query(updateQuery, [
                data.azureId,
                new Date().toISOString(),
                data.recordId
            ]);
            if (!updateResult || updateResult.length === 0) {
                throw new common_1.NotFoundException(`Record with ID ${data.recordId} not found in table ${data.tableName}`);
            }
            return {
                success: true,
                message: `Updated ${data.tableName} record with Azure ID: ${data.azureId}`,
                entity: updateResult[0]
            };
        }
        catch (error) {
            this.logger.error(`Error updating Azure ID: ${error.message}`);
            throw new common_1.InternalServerErrorException(`Failed to update Azure ID: ${error.message}`);
        }
    }
    async createMissingEvent(data) {
        this.logger.log(`Creating missing event with Azure ID: ${data.azureId}`);
        try {
            const sql = require('mssql');
            const pool = await this.checkAzureDbConnection();
            try {
                const eventQuery = await pool.request()
                    .input('eventId', sql.UniqueIdentifier, data.azureId)
                    .query(`
            SELECT 
              e.Id, e.Name, e.Description, e.EventStartDate, e.EventStartTime, 
              e.EventEndDate, e.EventEndTime, e.VenueId, e.ProfileImageCropperValue,
              e.Url, e.FeatureLevel, e.CreatedOn, e.UpdatedOn,
              CONVERT(varchar, e.EventStartDate, 23) AS EventStartDateStr,
              CONVERT(varchar, e.EventStartTime, 8) AS EventStartTimeStr,
              CONVERT(varchar, e.EventEndDate, 23) AS EventEndDateStr,
              CONVERT(varchar, e.EventEndTime, 8) AS EventEndTimeStr,
              v.Name as VenueName
            FROM PerformanceEvents e
            LEFT JOIN Venues v ON e.VenueId = v.Id
            WHERE e.Id = @eventId
          `);
                if (!eventQuery.recordset || eventQuery.recordset.length === 0) {
                    throw new common_1.NotFoundException(`Event with Azure ID ${data.azureId} not found`);
                }
                const azureEvent = eventQuery.recordset[0];
                this.logger.log(`Found Azure event: ${azureEvent.Name}`);
                const startDateTime = this.combineDateTime(azureEvent.EventStartDateStr, azureEvent.EventStartTimeStr);
                const endDateTime = this.combineDateTime(azureEvent.EventEndDateStr || azureEvent.EventStartDateStr, azureEvent.EventEndTimeStr);
                let venueId = null;
                let venueName = 'Unknown Venue';
                if (azureEvent.VenueId && azureEvent.VenueId !== '' && azureEvent.VenueId !== 'null') {
                    let venueResult = [];
                    try {
                        const venueQuery = `SELECT id, name FROM venue WHERE azure_id::text = $1 AND deleted = FALSE`;
                        venueResult = await this.dataSource.query(venueQuery, [azureEvent.VenueId]);
                    }
                    catch (error) {
                        this.logger.warn(`Error in venue lookup for ${azureEvent.VenueId}: ${error.message}, trying name-based lookup`);
                        venueResult = [];
                    }
                    if (venueResult && venueResult.length > 0) {
                        venueId = venueResult[0].id;
                        venueName = venueResult[0].name;
                        this.logger.log(`Found matching venue in PostgreSQL with ID: ${venueId}, Name: ${venueName}`);
                    }
                    else {
                        if (azureEvent.VenueName) {
                            const venueNameQuery = `SELECT id, name FROM venue WHERE name ILIKE $1 AND deleted = FALSE`;
                            const venueNameResult = await this.dataSource.query(venueNameQuery, [`%${azureEvent.VenueName}%`]);
                            if (venueNameResult && venueNameResult.length > 0) {
                                venueId = venueNameResult[0].id;
                                venueName = venueNameResult[0].name;
                                this.logger.log(`Found venue by name in PostgreSQL with ID: ${venueId}, Name: ${venueName}`);
                            }
                            else {
                                this.logger.log(`No matching venue found in PostgreSQL for Azure ID: ${azureEvent.VenueId} or name: ${azureEvent.VenueName}`);
                                if (azureEvent.VenueName) {
                                    const createVenueResult = await this.dataSource.query(`INSERT INTO venue (name, azure_id, created_at, updated_at, deleted)
                     VALUES ($1, $2, $3, $4, $5)
                     RETURNING id`, [
                                        azureEvent.VenueName,
                                        azureEvent.VenueId,
                                        new Date().toISOString(),
                                        new Date().toISOString(),
                                        false
                                    ]);
                                    if (createVenueResult && createVenueResult.length > 0) {
                                        venueId = createVenueResult[0].id;
                                        venueName = azureEvent.VenueName;
                                        this.logger.log(`Created new venue in PostgreSQL with ID: ${venueId}, Name: ${venueName}`);
                                    }
                                }
                            }
                        }
                    }
                }
                else if (azureEvent.VenueName) {
                    const venueNameQuery = `SELECT id, name FROM venue WHERE name ILIKE $1 AND deleted = FALSE`;
                    const venueNameResult = await this.dataSource.query(venueNameQuery, [`%${azureEvent.VenueName}%`]);
                    if (venueNameResult && venueNameResult.length > 0) {
                        venueId = venueNameResult[0].id;
                        venueName = venueNameResult[0].name;
                        this.logger.log(`Found venue by name in PostgreSQL with ID: ${venueId}, Name: ${venueName}`);
                    }
                    else if (azureEvent.VenueName) {
                        const createVenueResult = await this.dataSource.query(`INSERT INTO venue (name, created_at, updated_at, deleted)
               VALUES ($1, $2, $3, $4)
               RETURNING id`, [
                            azureEvent.VenueName,
                            new Date().toISOString(),
                            new Date().toISOString(),
                            false
                        ]);
                        if (createVenueResult && createVenueResult.length > 0) {
                            venueId = createVenueResult[0].id;
                            venueName = azureEvent.VenueName;
                            this.logger.log(`Created new venue in PostgreSQL with ID: ${venueId}, Name: ${venueName}`);
                        }
                    }
                }
                let imageUrl = '';
                if (azureEvent.ProfileImageCropperValue && typeof azureEvent.ProfileImageCropperValue === 'string') {
                    try {
                        const imageData = JSON.parse(azureEvent.ProfileImageCropperValue);
                        if (imageData && imageData.Src) {
                            const imagePath = imageData.Src;
                            imageUrl = imagePath.startsWith('http')
                                ? imagePath
                                : `https://tucsonlovesmusic.com${imagePath}`;
                        }
                    }
                    catch (e) {
                        this.logger.error(`Error parsing ProfileImageCropperValue: ${e.message}`);
                    }
                }
                const queryRunner = this.dataSource.createQueryRunner();
                await queryRunner.connect();
                await queryRunner.startTransaction();
                try {
                    let venueIds = [];
                    if (venueId || (azureEvent.VenueId && azureEvent.VenueId !== '' && azureEvent.VenueId !== 'null')) {
                        try {
                            const conditions = [];
                            const params = [];
                            if (venueId) {
                                conditions.push('id = $' + (params.length + 1));
                                params.push(venueId);
                            }
                            if (azureEvent.VenueId && azureEvent.VenueId !== '' && azureEvent.VenueId !== 'null') {
                                conditions.push('(azure_id::text = $' + (params.length + 1) + ' AND azure_id IS NOT NULL)');
                                params.push(azureEvent.VenueId);
                                conditions.push(`LOWER(TRIM(name)) IN (
                  SELECT LOWER(TRIM(name))
                  FROM venue
                  WHERE azure_id::text = $${params.length} AND deleted IS NOT TRUE
                )`);
                            }
                            venueIds = await queryRunner.query(`
                SELECT DISTINCT id
                FROM venue
                WHERE 
                  deleted IS NOT TRUE
                  AND (${conditions.join(' OR ')})
              `, params);
                        }
                        catch (error) {
                            this.logger.warn(`Error in venue lookup for ${azureEvent.VenueId}: ${error.message}, using fallback`);
                            venueIds = await queryRunner.query(`
                SELECT DISTINCT id FROM venue WHERE deleted IS NOT TRUE
              `);
                        }
                    }
                    else {
                        venueIds = await queryRunner.query(`
              SELECT DISTINCT id FROM venue WHERE deleted IS NOT TRUE
            `);
                    }
                    const venueIdList = venueIds.map(v => v.id);
                    const existingEventCheck = await queryRunner.query(`
            SELECT id, name, azure_id, "startDateTime", venue_id
            FROM event
            WHERE 
              deleted IS NOT TRUE
              AND name = $1
              AND venue_id = ANY($2::uuid[])
              AND "startDateTime" = $3
          `, [azureEvent.Name, venueIdList, startDateTime]);
                    if (existingEventCheck.length > 0) {
                        const existingEvent = existingEventCheck[0];
                        this.logger.log(`Found existing event for Azure event ${azureEvent.Id} (${azureEvent.Name}): ${existingEvent.id} (${existingEvent.name})`);
                        if (!existingEvent.azure_id) {
                            await queryRunner.query(`
                UPDATE event
                SET 
                  azure_id = $1,
                  updated_at = NOW(),
                  id_validated = TRUE,
                  dedup_validated = TRUE, 
                  last_validation_date = NOW(),
                  validation_version = COALESCE(validation_version, 0) + 1
                WHERE id = $2
              `, [azureEvent.Id, existingEvent.id]);
                            await queryRunner.commitTransaction();
                            return {
                                success: true,
                                message: `Updated existing event with Azure ID: ${azureEvent.Name}`,
                                postgresId: existingEvent.id,
                                azureId: azureEvent.Id,
                                venueName: venueName,
                                venueId: venueId,
                                action: 'updated_existing'
                            };
                        }
                        else {
                            await queryRunner.rollbackTransaction();
                            this.logger.warn(`Event ${existingEvent.id} already has Azure ID ${existingEvent.azure_id}`);
                            return {
                                success: false,
                                message: `Event already exists with Azure ID: ${azureEvent.Name}`,
                                postgresId: existingEvent.id,
                                azureId: existingEvent.azure_id,
                                action: 'already_exists'
                            };
                        }
                    }
                    const result = await queryRunner.manager.query(`INSERT INTO event (
              name, description, "startDateTime", "endDateTime", venue_id, 
              "imageUrl", azure_id, featured, created_at, updated_at, deleted,
              id_validated, dedup_validated, last_validation_date, validation_version
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
            RETURNING id`, [
                        azureEvent.Name,
                        azureEvent.Description || '',
                        startDateTime,
                        endDateTime,
                        venueId,
                        imageUrl,
                        azureEvent.Id,
                        azureEvent.FeatureLevel > 0 ? (azureEvent.FeatureLevel / 100) : 0.00,
                        azureEvent.CreatedOn ? new Date(azureEvent.CreatedOn).toISOString() : new Date().toISOString(),
                        azureEvent.UpdatedOn ? new Date(azureEvent.UpdatedOn).toISOString() : new Date().toISOString(),
                        false,
                        true,
                        true,
                        new Date().toISOString(),
                        1
                    ]);
                    await queryRunner.commitTransaction();
                    return {
                        success: true,
                        message: `Created new event in PostgreSQL: ${azureEvent.Name}`,
                        postgresId: result[0].id,
                        azureId: azureEvent.Id,
                        venueName: venueName,
                        venueId: venueId,
                        action: 'created_new'
                    };
                }
                catch (error) {
                    await queryRunner.rollbackTransaction();
                    this.logger.error(`Error creating event in PostgreSQL: ${error.message}`);
                    throw new common_1.InternalServerErrorException(`Failed to create event in PostgreSQL: ${error.message}`);
                }
                finally {
                    await queryRunner.release();
                }
            }
            finally {
                if (pool) {
                    await pool.close();
                }
            }
        }
        catch (error) {
            this.logger.error(`Error in createMissingEvent: ${error.message}`);
            throw new common_1.InternalServerErrorException(`Error creating missing event: ${error.message}`);
        }
    }
    async resolveIssue(issueId, fixDto) {
        fixDto.issueId = issueId;
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const { issueId, resolution, matchId, azureId, mergeFields } = fixDto;
            this.logger.log(`Resolving issue ${issueId} with resolution: ${resolution}`);
            if (resolution === 'fix') {
                const finalAzureId = azureId || this.generateMockAzureId();
                const isUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(issueId);
                const isNumeric = /^\d+$/.test(issueId);
                if (!isUuid && !isNumeric) {
                    throw new Error(`Invalid ID format: ${issueId}. Must be a UUID or numeric ID.`);
                }
                this.logger.log(`Processing ID: ${issueId} (format: ${isUuid ? 'UUID' : 'numeric'})`);
                const venueCheck = await queryRunner.query(`SELECT id, name, google_place_id, address, city, state, website FROM venue WHERE id::text = $1 AND deleted IS NOT TRUE`, [issueId]);
                const musicianCheck = await queryRunner.query(`SELECT id, name, bio, category, genre, "imageUrl", email, "phoneNumber", website, "socialMedia" FROM talent WHERE id::text = $1`, [issueId]);
                const eventCheck = await queryRunner.query(`SELECT id FROM event WHERE id::text = $1 AND deleted IS NOT TRUE`, [issueId]);
                if (venueCheck.length > 0) {
                    this.logger.log(`Fixing venue "${venueCheck[0].name}" with ${azureId ? 'provided' : 'new'} Azure ID: ${finalAzureId}`);
                    if (azureId && mergeFields) {
                        try {
                            const azureVenue = await this.fetchAzureVenueForMerge(azureId);
                            const azureData = {
                                website: venueCheck[0].website,
                                googlePlaceId: venueCheck[0].google_place_id,
                                address: venueCheck[0].address,
                                city: venueCheck[0].city,
                                state: venueCheck[0].state,
                                zipCode: venueCheck[0].zipCode,
                                latitude: venueCheck[0].latitude,
                                longitude: venueCheck[0].longitude
                            };
                            if (azureVenue) {
                                if (!azureData.website && azureVenue.Website) {
                                    azureData.website = azureVenue.Website;
                                }
                                if (!azureData.address && azureVenue.Address) {
                                    azureData.address = azureVenue.Address;
                                }
                                if (!azureData.city && azureVenue.City) {
                                    azureData.city = azureVenue.City;
                                }
                                if (!azureData.state && azureVenue.State) {
                                    azureData.state = azureVenue.State;
                                }
                                if (!azureData.zipCode && azureVenue.PostalCode) {
                                    azureData.zipCode = azureVenue.PostalCode;
                                }
                                if ((!azureData.latitude || !azureData.longitude) &&
                                    azureVenue.Latitude && azureVenue.Longitude) {
                                    azureData.latitude = azureVenue.Latitude;
                                    azureData.longitude = azureVenue.Longitude;
                                }
                            }
                            this.logger.log(`Merging venue data with Azure record: ${JSON.stringify(azureData)}`);
                            await queryRunner.query(`
                UPDATE venue 
                SET azure_id = $1,
                    website = COALESCE($2, website),
                    address = COALESCE($3, address),
                    city = COALESCE($4, city),
                    state = COALESCE($5, state),
                    "zipCode" = COALESCE($6, "zipCode"),
                    latitude = COALESCE($7, latitude),
                    longitude = COALESCE($8, longitude),
                    updated_at = NOW()
                WHERE id::text = $9
              `, [
                                finalAzureId,
                                azureData.website,
                                azureData.address,
                                azureData.city,
                                azureData.state,
                                azureData.zipCode,
                                azureData.latitude,
                                azureData.longitude,
                                issueId
                            ]);
                        }
                        catch (error) {
                            this.logger.error(`Error fetching Azure venue data: ${error.message}`);
                            await queryRunner.query(`
                UPDATE venue 
                SET azure_id = $1,
                    updated_at = NOW()
                WHERE id::text = $2
              `, [finalAzureId, issueId]);
                        }
                    }
                    else {
                        await queryRunner.query(`
              UPDATE venue 
              SET azure_id = $1,
                  updated_at = NOW()
              WHERE id::text = $2
            `, [finalAzureId, issueId]);
                    }
                }
                else if (musicianCheck.length > 0) {
                    this.logger.log(`Fixing musician with ID ${issueId} with ${azureId ? 'provided' : 'new'} Azure ID: ${finalAzureId}`);
                    if (azureId && mergeFields) {
                        try {
                            const azureTalent = await this.fetchAzureTalentForMerge(azureId);
                            const azureData = {
                                name: musicianCheck[0].name,
                                bio: musicianCheck[0].bio,
                                category: musicianCheck[0].category,
                                genre: musicianCheck[0].genre,
                                imageUrl: musicianCheck[0].imageUrl,
                                email: musicianCheck[0].email,
                                phoneNumber: musicianCheck[0].phoneNumber,
                                website: musicianCheck[0].website,
                                socialMedia: musicianCheck[0].socialMedia
                            };
                            if (azureTalent) {
                                this.logger.log(`Merging data from Azure talent ${azureTalent.Name} into PostgreSQL talent ${musicianCheck[0].name}`);
                                if (azureTalent.Name) {
                                    azureData.name = azureTalent.Name;
                                }
                                if (azureTalent.Bio) {
                                    azureData.bio = azureTalent.Bio;
                                }
                                if (azureTalent.Category) {
                                    azureData.category = azureTalent.Category;
                                }
                                if (azureTalent.Genre) {
                                    azureData.genre = azureTalent.Genre;
                                }
                                if (azureTalent.ImageUrl) {
                                    azureData.imageUrl = azureTalent.ImageUrl;
                                }
                                if (azureTalent.Email) {
                                    azureData.email = azureTalent.Email;
                                }
                                if (azureTalent.PhoneNumber) {
                                    azureData.phoneNumber = azureTalent.PhoneNumber;
                                }
                                if (azureTalent.Website) {
                                    azureData.website = azureTalent.Website;
                                }
                                if (azureTalent.SocialMedia) {
                                    azureData.socialMedia = typeof azureTalent.SocialMedia === 'string'
                                        ? azureTalent.SocialMedia
                                        : JSON.stringify(azureTalent.SocialMedia);
                                }
                            }
                            this.logger.log(`Merging talent data with Azure record: ${JSON.stringify(azureData)}`);
                            await queryRunner.query(`
                UPDATE talent 
                SET azure_id = $1,
                    name = COALESCE($2, name),
                    bio = COALESCE($3, bio),
                    category = COALESCE($4, category),
                    genre = COALESCE($5, genre),
                    "imageUrl" = COALESCE($6, "imageUrl"),
                    email = COALESCE($7, email),
                    "phoneNumber" = COALESCE($8, "phoneNumber"),
                    website = COALESCE($9, website),
                    "socialMedia" = COALESCE($10, "socialMedia"),
                    "updatedAt" = NOW()
                WHERE id::text = $11
              `, [
                                finalAzureId,
                                azureData.name,
                                azureData.bio,
                                azureData.category,
                                azureData.genre,
                                azureData.imageUrl,
                                azureData.email,
                                azureData.phoneNumber,
                                azureData.website,
                                azureData.socialMedia,
                                issueId
                            ]);
                        }
                        catch (error) {
                            this.logger.error(`Error fetching Azure talent data: ${error.message}`);
                            await queryRunner.query(`
                UPDATE talent 
                SET azure_id = $1,
                    "updatedAt" = NOW()
                WHERE id::text = $2
              `, [finalAzureId, issueId]);
                        }
                    }
                    else {
                        await queryRunner.query(`
              UPDATE talent 
              SET azure_id = $1,
                  "updatedAt" = NOW()
              WHERE id::text = $2
            `, [finalAzureId, issueId]);
                    }
                }
                else if (eventCheck.length > 0) {
                    this.logger.log(`Fixing event with ID ${issueId} with ${azureId ? 'provided' : 'new'} Azure ID: ${finalAzureId}`);
                    if (azureId && mergeFields) {
                        try {
                            const azureEvent = await this.fetchAzureEventForMerge(azureId);
                            const fullEventCheck = await queryRunner.query(`
                SELECT e.id, e.name, e.description, e."startDateTime", e."endDateTime", 
                       e.venue_id, e."imageUrl", e.media, e.featured,
                       e.status
                FROM event e WHERE e.id::text = $1 AND e.deleted IS NOT TRUE
              `, [issueId]);
                            if (fullEventCheck.length === 0) {
                                throw new Error(`Event with ID ${issueId} not found in PostgreSQL`);
                            }
                            const pgEvent = fullEventCheck[0];
                            const azureData = {
                                name: pgEvent.name,
                                description: pgEvent.description,
                                startDateTime: pgEvent.startDateTime,
                                endDateTime: pgEvent.endDateTime,
                                venue_id: pgEvent.venue_id,
                                imageUrl: pgEvent.imageUrl,
                                media: pgEvent.media,
                                featured: pgEvent.featured,
                            };
                            if (azureEvent) {
                                this.logger.log(`Merging data from Azure event ${azureEvent.Name}`);
                                if (azureEvent.Name) {
                                    azureData.name = azureEvent.Name;
                                }
                                if (azureEvent.Description) {
                                    azureData.description = azureEvent.Description;
                                }
                                if (azureEvent.StartDateTimeStr) {
                                    azureData.startDateTime = azureEvent.StartDateTimeStr;
                                    this.logger.log(`Using Azure start date/time: ${azureEvent.StartDateTimeStr}`);
                                }
                                else if (azureEvent.StartDateTime) {
                                    azureData.startDateTime = azureEvent.StartDateTime;
                                    this.logger.log(`Using Azure start date/time (ISO): ${azureEvent.StartDateTime}`);
                                }
                                if (azureEvent.EndDateTimeStr) {
                                    azureData.endDateTime = azureEvent.EndDateTimeStr;
                                    this.logger.log(`Using Azure end date/time: ${azureEvent.EndDateTimeStr}`);
                                }
                                else if (azureEvent.EndDateTime) {
                                    azureData.endDateTime = azureEvent.EndDateTime;
                                    this.logger.log(`Using Azure end date/time (ISO): ${azureEvent.EndDateTime}`);
                                }
                                if (azureEvent.ImageUrl) {
                                    azureData.imageUrl = azureEvent.ImageUrl;
                                }
                                if (azureEvent.Media) {
                                    if (azureData.media) {
                                        try {
                                            const existingMedia = typeof azureData.media === 'string'
                                                ? JSON.parse(azureData.media)
                                                : azureData.media;
                                            const azureMedia = typeof azureEvent.Media === 'string'
                                                ? JSON.parse(azureEvent.Media)
                                                : azureEvent.Media;
                                            azureData.media = JSON.stringify({ ...existingMedia, ...azureMedia });
                                        }
                                        catch (e) {
                                            this.logger.error(`Error merging media JSON: ${e.message}`);
                                            azureData.media = azureEvent.Media;
                                        }
                                    }
                                    else {
                                        azureData.media = azureEvent.Media;
                                    }
                                }
                                if (azureEvent.Featured !== undefined) {
                                    azureData.featured = azureEvent.Featured;
                                }
                            }
                            this.logger.log(`Merging event data with Azure record: ${JSON.stringify(azureData)}`);
                            await queryRunner.query(`
                UPDATE event 
                SET azure_id = $1,
                    name = COALESCE($2, name),
                    description = COALESCE($3, description),
                    "startDateTime" = COALESCE($4, "startDateTime"),
                    "endDateTime" = COALESCE($5, "endDateTime"),
                    "imageUrl" = COALESCE($6, "imageUrl"),
                    media = COALESCE($7, media),
                    featured = COALESCE($8, featured),

                    updated_at = NOW(),
                    id_validated = TRUE,
                    dedup_validated = TRUE,
                    last_validation_date = NOW(),
                    validation_version = validation_version + 1
                WHERE id::text = $9
              `, [
                                finalAzureId,
                                azureData.name,
                                azureData.description,
                                azureData.startDateTime,
                                azureData.endDateTime,
                                azureData.imageUrl,
                                azureData.media,
                                azureData.featured,
                                issueId
                            ]);
                            this.logger.log(`Event ${issueId} has been marked as fully validated to prevent cyclical validation`);
                        }
                        catch (error) {
                            this.logger.error(`Error fetching Azure event data: ${error.message}`);
                            await queryRunner.query(`
                UPDATE event 
                SET azure_id = $1,
                    updated_at = NOW(),
                    id_validated = TRUE,
                    dedup_validated = TRUE,
                    last_validation_date = NOW(),
                    validation_version = validation_version + 1
                WHERE id::text = $2
              `, [finalAzureId, issueId]);
                            this.logger.log(`Event ${issueId} has been marked as fully validated (with Azure ID only) to prevent cyclical validation`);
                        }
                    }
                    else {
                        await queryRunner.query(`
              UPDATE event 
              SET azure_id = $1,
                  updated_at = NOW(),
                  id_validated = TRUE,
                  dedup_validated = TRUE,
                  last_validation_date = NOW(),
                  validation_version = validation_version + 1
              WHERE id::text = $2
            `, [finalAzureId, issueId]);
                        this.logger.log(`Event ${issueId} has been marked as fully validated with Azure ID: ${finalAzureId}`);
                    }
                }
                else {
                    throw new Error(`Entity with ID ${issueId} not found`);
                }
            }
            else if (resolution === 'merge') {
                if (!matchId) {
                    throw new Error('Match ID is required for merge operations');
                }
                const isValidMatchUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(matchId);
                if (!isValidMatchUuid) {
                    throw new Error(`Invalid UUID format for matchId: ${matchId}`);
                }
                const venueCheck = await queryRunner.query(`SELECT id, name FROM venue WHERE id::text = $1`, [issueId]);
                const musicianCheck = await queryRunner.query(`SELECT id, name, bio, category, genre, "imageUrl", email, "phoneNumber", website, "socialMedia" FROM talent WHERE id::text = $1`, [issueId]);
                const eventCheck = await queryRunner.query(`SELECT id FROM event WHERE id::text = $1 AND deleted IS NOT TRUE`, [issueId]);
                if (venueCheck.length > 0) {
                    this.logger.log(`Merging venue "${venueCheck[0].name}" with Azure ID: ${matchId}`);
                    const targetVenue = await queryRunner.query(`SELECT id, name FROM venue WHERE azure_id::text = $1`, [matchId]);
                    if (targetVenue.length > 0) {
                        this.logger.log(`Found target venue "${targetVenue[0].name}" for merge`);
                        await queryRunner.query(`
              UPDATE event 
              SET venue_id = $1,
                  updated_at = NOW()
              WHERE venue_id::text = $2
            `, [targetVenue[0].id, issueId]);
                        const azureVenue = await this.fetchAzureVenueForMerge(matchId);
                        if (azureVenue && azureVenue.Locale) {
                            try {
                                let address = '';
                                let city = '';
                                let state = '';
                                let zipCode = '';
                                let latitude = null;
                                let longitude = null;
                                let formatted_address = '';
                                if (azureVenue.Locale.MapLocation && azureVenue.Locale.MapLocation.address) {
                                    const addressData = azureVenue.Locale.MapLocation.address;
                                    if (addressData.streetNumber && addressData.street) {
                                        address = `${addressData.streetNumber} ${addressData.street}`;
                                    }
                                    city = addressData.city || '';
                                    state = addressData.state || '';
                                    zipCode = addressData.postalcode || '';
                                    formatted_address = addressData.full_address || '';
                                    if (addressData.coordinates) {
                                        latitude = addressData.coordinates.lat;
                                        longitude = addressData.coordinates.lng;
                                    }
                                    const updateFields = [];
                                    const updateValues = [];
                                    let paramIndex = 1;
                                    if (address && address.trim() !== '') {
                                        updateFields.push(`address = $${paramIndex}`);
                                        updateValues.push(address);
                                        paramIndex++;
                                    }
                                    if (city && city.trim() !== '') {
                                        updateFields.push(`city = $${paramIndex}`);
                                        updateValues.push(city);
                                        paramIndex++;
                                    }
                                    if (state && state.trim() !== '') {
                                        updateFields.push(`state = $${paramIndex}`);
                                        updateValues.push(state);
                                        paramIndex++;
                                    }
                                    if (zipCode && zipCode.trim() !== '') {
                                        updateFields.push(`"zipCode" = $${paramIndex}`);
                                        updateValues.push(zipCode);
                                        paramIndex++;
                                    }
                                    if (formatted_address && formatted_address.trim() !== '') {
                                        updateFields.push(`formatted_address = $${paramIndex}`);
                                        updateValues.push(formatted_address);
                                        paramIndex++;
                                    }
                                    if (latitude !== null) {
                                        updateFields.push(`latitude = $${paramIndex}`);
                                        updateValues.push(latitude);
                                        paramIndex++;
                                    }
                                    if (longitude !== null) {
                                        updateFields.push(`longitude = $${paramIndex}`);
                                        updateValues.push(longitude);
                                        paramIndex++;
                                    }
                                    if (updateFields.length > 0) {
                                        updateValues.push(targetVenue[0].id);
                                        await queryRunner.query(`
                      UPDATE venue 
                      SET ${updateFields.join(', ')},
                          updated_at = NOW()
                      WHERE id = $${paramIndex}
                    `, updateValues);
                                        this.logger.log(`Updated target venue with address data from Azure`);
                                    }
                                }
                            }
                            catch (error) {
                                this.logger.error(`Error extracting address data: ${error.message}`);
                            }
                        }
                        await queryRunner.query(`
              UPDATE venue 
              SET is_deleted = true,
                  azure_id = $1,
                  updated_at = NOW()
              WHERE id::text = $2
            `, [matchId, issueId]);
                        this.logger.log(`Marked duplicate venue as deleted and updated references`);
                    }
                    else {
                        await queryRunner.query(`
              UPDATE venue 
              SET azure_id = $1,
                  updated_at = NOW()
              WHERE id::text = $2
            `, [matchId, issueId]);
                    }
                }
                else if (musicianCheck.length > 0) {
                    const targetMusician = await queryRunner.query(`SELECT id, name FROM talent WHERE azure_id::text = $1`, [matchId]);
                    if (targetMusician.length > 0) {
                        await queryRunner.query(`
              UPDATE events_talents 
              SET talent_id = $1
              WHERE talent_id::text = $2
            `, [targetMusician[0].id, issueId]);
                        await queryRunner.query(`
              UPDATE talent 
              SET is_deleted = true,
                  azure_id = $1,
                  updated_at = NOW()
              WHERE id::text = $2
            `, [matchId, issueId]);
                    }
                    else {
                        await queryRunner.query(`
              UPDATE talent 
              SET azure_id = $1,
                  "updatedAt" = NOW()
              WHERE id::text = $2
            `, [matchId, issueId]);
                    }
                }
                else if (eventCheck.length > 0) {
                    const targetEvent = await queryRunner.query(`SELECT id, name FROM event WHERE azure_id::text = $1 AND deleted IS NOT TRUE`, [matchId]);
                    if (targetEvent.length > 0) {
                        await queryRunner.query(`
              UPDATE events_talents 
              SET event_id = $1
              WHERE event_id::text = $2
            `, [targetEvent[0].id, issueId]);
                        await queryRunner.query(`
              UPDATE event 
              SET is_deleted = true,
                  azure_id = $1,
                  updated_at = NOW()
              WHERE id::text = $2
            `, [matchId, issueId]);
                    }
                    else {
                        await queryRunner.query(`
              UPDATE event 
              SET azure_id = $1,
                  updated_at = NOW()
              WHERE id::text = $2
            `, [matchId, issueId]);
                    }
                }
                else {
                    throw new Error(`Entity with ID ${issueId} not found`);
                }
            }
            await queryRunner.commitTransaction();
            return { success: true, message: 'Issue resolved successfully' };
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error(`Error resolving issue: ${error.message}`);
            throw new common_1.InternalServerErrorException(`Failed to resolve issue: ${error.message}`);
        }
        finally {
            await queryRunner.release();
        }
    }
    generateMockAzureId() {
        const s4 = () => Math.floor((1 + Math.random()) * 0x10000).toString(16).substring(1);
        return `${s4()}${s4()}-${s4()}-${s4()}-${s4()}-${s4()}${s4()}${s4()}`;
    }
    sanitizeForSql(str) {
        if (!str)
            return '';
        return str.replace(/'/g, "''");
    }
    async getMissingEvents() {
        this.logger.log('Fetching all event ID validation issues');
        try {
            const sql = require('mssql');
            const pool = await this.checkAzureDbConnection();
            try {
                const issues = [];
                this.logger.log('Finding events in MSSQL that don\'t exist in PostgreSQL');
                const allEventsQuery = `
          SELECT 
            Id, 
            Name, 
            EventStartDate,
            EventStartTime,
            CONVERT(varchar, EventStartDate, 23) AS EventStartDateStr,
            CONVERT(varchar, EventStartTime, 8) AS EventStartTimeStr,
            VenueId, 
            Description
          FROM PerformanceEvents
        `;
                const allEvents = await pool.request().query(allEventsQuery);
                this.logger.log(`Found ${allEvents.recordset.length} total events in MSSQL`);
                const pgEventsQuery = `
          SELECT e.id, e.azure_id, e.name, e.description, e."startDateTime", e.venue_id, e.id_validated,
                 v.name as venue_name
          FROM event e
          LEFT JOIN venue v ON e.venue_id = v.id
          WHERE e.deleted IS NOT TRUE
        `;
                const pgEvents = await this.dataSource.query(pgEventsQuery);
                const pgAzureIds = new Set();
                const pgEventsWithoutAzureId = [];
                for (const pgEvent of pgEvents) {
                    if (pgEvent.azure_id) {
                        pgAzureIds.add(pgEvent.azure_id.toUpperCase());
                    }
                    else if (pgEvent.id_validated !== true) {
                        pgEventsWithoutAzureId.push(pgEvent);
                    }
                }
                this.logger.log(`Found ${pgEventsWithoutAzureId.length} events in PostgreSQL missing Azure IDs`);
                const missingEvents = [];
                for (const event of allEvents.recordset) {
                    const azureEventIdUpper = event.Id.toUpperCase();
                    if (!pgAzureIds.has(azureEventIdUpper)) {
                        missingEvents.push(event);
                    }
                }
                this.logger.log(`Found ${missingEvents.length} events in MSSQL that don't exist in PostgreSQL`);
                this.logger.log('Fetching all venue information from MSSQL...');
                const venueMap = new Map();
                try {
                    const allVenuesQuery = `SELECT Id, Name FROM Venues`;
                    const allVenues = await pool.request().query(allVenuesQuery);
                    this.logger.log(`Found ${allVenues.recordset.length} venues in MSSQL`);
                    for (const venue of allVenues.recordset) {
                        if (venue.Id && venue.Name) {
                            venueMap.set(venue.Id.toUpperCase(), venue.Name);
                        }
                    }
                }
                catch (venueError) {
                    this.logger.error(`Error fetching venue information: ${venueError.message}`);
                }
                this.logger.log('Creating validation issues for events missing in PostgreSQL...');
                missingEvents.sort((a, b) => {
                    const dateA = a.EventStartDateStr || '0000-00-00';
                    const dateB = b.EventStartDateStr || '0000-00-00';
                    return dateB.localeCompare(dateA);
                });
                for (const event of missingEvents) {
                    const formattedDate = event.EventStartDateStr || 'Unknown';
                    const venueName = event.VenueId ?
                        (venueMap.get(event.VenueId.toUpperCase()) || 'Unknown Venue') : 'Unknown Venue';
                    if (event.VenueId && !venueMap.has(event.VenueId.toUpperCase())) {
                        this.logger.warn(`Could not find venue with ID ${event.VenueId} for event ${event.Name}`);
                    }
                    const startDateTimeStr = event.EventStartDateStr && event.EventStartTimeStr ?
                        `${event.EventStartDateStr} ${event.EventStartTimeStr}` :
                        (event.EventStartDateStr ? `${event.EventStartDateStr} 00:00:00` : null);
                    const azureRecord = {
                        id: event.Id,
                        name: event.Name || 'Unknown Event',
                        description: event.Description || '',
                        startDateTime: startDateTimeStr,
                        venue_name: venueName
                    };
                    issues.push({
                        id: this.generateMockAzureId(),
                        entityType: 'events',
                        postgresRecord: null,
                        azureRecord: azureRecord,
                        issueType: 'missing_postgres',
                        description: `Event "${event.Name || 'Unknown Event'}" exists in MSSQL but not in PostgreSQL. Event date: ${formattedDate}`,
                        createdAt: new Date().toISOString()
                    });
                }
                this.logger.log('Creating validation issues for events missing Azure IDs...');
                this.logger.log('Fetching all venue information from PostgreSQL...');
                const pgVenueMap = new Map();
                try {
                    const allVenuesQuery = 'SELECT id, name FROM venue WHERE deleted = FALSE';
                    const allVenues = await this.dataSource.query(allVenuesQuery);
                    this.logger.log(`Found ${allVenues.length} venues in PostgreSQL`);
                    for (const venue of allVenues) {
                        if (venue.id && venue.name) {
                            pgVenueMap.set(venue.id, venue.name);
                        }
                    }
                }
                catch (venueError) {
                    this.logger.error(`Error fetching PostgreSQL venue information: ${venueError.message}`);
                }
                for (const event of pgEventsWithoutAzureId) {
                    const formattedDate = event.startDateTime ?
                        (event.startDateTime.includes('T') ?
                            event.startDateTime.split('T')[0] :
                            event.startDateTime.split(' ')[0]) : 'Unknown';
                    let venueName = 'Unknown Venue';
                    if (event.azure_id && !event.venue_name) {
                        try {
                            const azureEventQuery = `
                SELECT e.Name, e.VenueId, v.Name as VenueName
                FROM PerformanceEvents e
                LEFT JOIN Venues v ON e.VenueId = v.Id  
                WHERE e.Id = '${this.sanitizeForSql(event.azure_id)}'
              `;
                            const azureResult = await pool.request().query(azureEventQuery);
                            if (azureResult.recordset && azureResult.recordset.length > 0) {
                                const azureEvent = azureResult.recordset[0];
                                venueName = azureEvent.VenueName || 'Unknown Venue';
                                this.logger.log(`Found venue from Azure for event ${event.name}: ${venueName}`);
                            }
                        }
                        catch (error) {
                            this.logger.warn(`Could not query Azure for venue info for event ${event.name}: ${error.message}`);
                        }
                    }
                    else if (event.venue_name) {
                        venueName = event.venue_name;
                    }
                    if (venueName === 'Unknown Venue') {
                        this.logger.warn(`Event ${event.name} has no venue information available in PostgreSQL or Azure`);
                    }
                    const pgRecord = {
                        id: event.id,
                        name: event.name || 'Unknown Event',
                        description: event.description || '',
                        startDateTime: event.startDateTime,
                        venue_name: venueName
                    };
                    issues.push({
                        id: this.generateMockAzureId(),
                        entityType: 'events',
                        postgresRecord: pgRecord,
                        azureRecord: null,
                        issueType: 'missing_azure',
                        description: `Event "${event.name || 'Unknown Event'}" exists in PostgreSQL but is missing an Azure ID. Event date: ${formattedDate}`,
                        createdAt: new Date().toISOString()
                    });
                }
                this.logger.log(`Created ${issues.length} validation issues for missing events`);
                this.logger.log(`Found ${issues.length} events in MSSQL that are missing from PostgreSQL`);
                return issues;
            }
            finally {
                if (pool) {
                    try {
                        await pool.close();
                        this.logger.log('MSSQL connection closed');
                    }
                    catch (closeError) {
                        this.logger.error(`Error closing MSSQL connection: ${closeError.message}`);
                    }
                }
            }
        }
        catch (error) {
            this.logger.error(`Error fetching missing events: ${error.message}`);
            throw new common_1.InternalServerErrorException('Failed to fetch missing events');
        }
    }
    async fetchAzureTalentForMerge(azureId) {
        const sql = require('mssql');
        let pool = null;
        try {
            const config = {
                server: 'mssql.drv1.umbhost.net',
                database: 'TLM',
                user: 'Reader',
                password: 'TLM1234!',
                options: {
                    encrypt: false,
                    trustServerCertificate: true
                }
            };
            pool = await sql.connect(config);
            this.logger.log(`Successfully connected to Azure SQL`);
            const result = await pool.request()
                .input('talentId', azureId)
                .query(`
          SELECT 
            Id, 
            Name,
            Description as Bio,
            FeatureLevel as Category,
            ContactName,
            ContactEmailAddress as Email,
            ContactPhoneNumber as PhoneNumber,
            ContactWebsite as Website,
            ProfileImageCropperValue,
            ProfileImageId,
            Url,
            CreatedOn as CreatedAt,
            UpdatedOn as UpdatedAt
          FROM Talent 
          WHERE Id = @talentId
        `);
            if (result.recordset.length === 0) {
                this.logger.error(`Talent with Azure ID ${azureId} not found`);
                return null;
            }
            const talent = result.recordset[0];
            if (talent.ProfileImageCropperValue && typeof talent.ProfileImageCropperValue === 'string') {
                try {
                    const imageData = JSON.parse(talent.ProfileImageCropperValue);
                    this.logger.log('Parsed ProfileImageCropperValue successfully');
                    if (imageData && imageData.Src) {
                        const imagePath = imageData.Src;
                        this.logger.log(`Found image path: ${imagePath}`);
                        talent.ImageUrl = imagePath.startsWith('http')
                            ? imagePath
                            : `https://tucsonlovesmusic.com${imagePath}`;
                        talent.ImageData = imageData;
                    }
                }
                catch (e) {
                    this.logger.error(`Error parsing talent ProfileImageCropperValue JSON: ${e.message}`);
                }
            }
            if (talent.SocialMedia && typeof talent.SocialMedia === 'string') {
                try {
                    talent.SocialMedia = JSON.parse(talent.SocialMedia);
                    this.logger.log('Parsed SocialMedia successfully');
                }
                catch (e) {
                    this.logger.error(`Error parsing talent SocialMedia JSON: ${e.message}`);
                }
            }
            return talent;
        }
        catch (error) {
            this.logger.error(`Error fetching Azure talent data: ${error.message}`);
            return null;
        }
        finally {
            if (pool) {
                await pool.close();
            }
        }
    }
    async fetchAzureVenueForMerge(azureId) {
        const sql = require('mssql');
        let pool = null;
        try {
            const config = {
                server: 'mssql.drv1.umbhost.net',
                database: 'TLM',
                user: 'Reader',
                password: 'TLM1234!',
                options: {
                    encrypt: false,
                    trustServerCertificate: true
                }
            };
            pool = await sql.connect(config);
            this.logger.log(`Successfully connected to Azure SQL`);
            const result = await pool.request()
                .input('venueId', azureId)
                .query(`
          SELECT 
            Id, 
            Name, 
            Locale,
            ContactWebsite as Website, 
            CreatedOn as CreatedAt,
            UpdatedOn as UpdatedAt
          FROM Venues 
          WHERE Id = @venueId
        `);
            if (result.recordset.length === 0) {
                this.logger.error(`Venue with Azure ID ${azureId} not found`);
                return null;
            }
            const venue = result.recordset[0];
            if (venue.Locale && typeof venue.Locale === 'string') {
                try {
                    venue.Locale = JSON.parse(venue.Locale);
                    this.logger.log('Parsed Locale successfully', venue.Locale);
                    if (venue.Locale.MapLocation?.address) {
                        const { address } = venue.Locale.MapLocation;
                        venue.Address = address.full_address || null;
                        venue.City = address.city || null;
                        venue.State = address.state || null;
                        venue.PostalCode = address.postalcode || null;
                        if (address.streetNumber && address.street) {
                            venue.StreetNumber = address.streetNumber;
                            venue.Street = address.street;
                            venue.StreetAddress = `${address.streetNumber} ${address.street}`;
                        }
                        if (address.full_address) {
                            this.logger.log(`Found address in Locale: ${address.full_address}`);
                        }
                        if (venue.Locale.MapLocation.lat && venue.Locale.MapLocation.lng) {
                            venue.Latitude = venue.Locale.MapLocation.lat;
                            venue.Longitude = venue.Locale.MapLocation.lng;
                        }
                        else if (address.coordinates && !address.coordinates.IsEmpty) {
                            venue.Latitude = address.coordinates.lat;
                            venue.Longitude = address.coordinates.lng;
                        }
                        else if (venue.Locale.lat && venue.Locale.lng) {
                            venue.Latitude = venue.Locale.lat;
                            venue.Longitude = venue.Locale.lng;
                        }
                    }
                }
                catch (e) {
                    this.logger.error(`Error parsing venue Locale JSON: ${e.message}`);
                }
            }
            return venue;
        }
        catch (error) {
            this.logger.error(`Error fetching Azure venue data: ${error.message}`);
            return null;
        }
        finally {
            if (pool) {
                await pool.close();
            }
        }
    }
    async deleteRecord(entityType, id) {
        this.logger.log(`Attempting to soft delete ${entityType} record with ID: ${id}`);
        if (!['venues', 'musicians', 'events'].includes(entityType)) {
            throw new common_1.BadRequestException(`Invalid entity type: ${entityType}`);
        }
        try {
            const entityTableMap = {
                'venues': 'venue',
                'musicians': 'talent',
                'events': 'event'
            };
            const tableName = entityTableMap[entityType];
            if (!tableName) {
                throw new common_1.BadRequestException(`Unknown entity type: ${entityType}`);
            }
            const checkQuery = `SELECT id FROM ${tableName} WHERE id = $1 AND deleted = false`;
            const checkResult = await this.dataSource.query(checkQuery, [id]);
            if (!checkResult || checkResult.length === 0) {
                throw new common_1.NotFoundException(`${entityType} record with ID ${id} not found or already deleted`);
            }
            const updateQuery = `
        UPDATE ${tableName} 
        SET deleted = true, 
            updated_at = NOW() 
        WHERE id = $1 
        RETURNING id
      `;
            const result = await this.dataSource.query(updateQuery, [id]);
            if (!result || result.length === 0) {
                throw new common_1.InternalServerErrorException(`Failed to soft delete ${entityType} record with ID ${id}`);
            }
            this.logger.log(`Successfully soft deleted ${entityType} record with ID: ${id}`);
            return {
                success: true,
                message: `${entityType.slice(0, -1)} has been moved to the Deleted Items section`,
                id: id
            };
        }
        catch (error) {
            this.logger.error(`Error soft deleting ${entityType} record: ${error.message}`, error.stack);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            else if (error.code === '23503') {
                throw new common_1.BadRequestException(`Cannot delete this ${entityType.slice(0, -1)} because it is referenced by other records. ` +
                    `Please remove any related records first.`);
            }
            else if (error instanceof common_1.BadRequestException || error instanceof common_1.InternalServerErrorException) {
                throw error;
            }
            else {
                throw new common_1.InternalServerErrorException(`Failed to delete ${entityType.slice(0, -1)}: ${error.message}`);
            }
        }
    }
    async fetchAzureEventForMerge(azureId) {
        const sql = require('mssql');
        let pool = null;
        try {
            const config = {
                server: 'mssql.drv1.umbhost.net',
                database: 'TLM',
                user: 'Reader',
                password: 'TLM1234!',
                options: {
                    encrypt: false,
                    trustServerCertificate: true
                }
            };
            pool = await sql.connect(config);
            this.logger.log(`Successfully connected to Azure SQL for event fetch`);
            const result = await pool.request()
                .input('eventId', sql.UniqueIdentifier, azureId)
                .query(`
          SELECT 
            Id, 
            Name, 
            Description,
            EventStartDate,
            EventStartTime,
            EventEndDate,
            EventEndTime,
            VenueId,
            FeatureLevel,
            ProfileImageCropperValue,
            ContactName,
            ContactEmailAddress,
            ContactPhoneNumber,
            ContactWebsite,
            Locale,
            Url,
            CreatedOn,
            UpdatedOn
          FROM PerformanceEvents 
          WHERE Id = @eventId
        `);
            if (result.recordset.length === 0) {
                this.logger.error(`Event with Azure ID ${azureId} not found`);
                return null;
            }
            const azureEvent = result.recordset[0];
            this.logger.log(`Raw Azure event data from SQL:`);
            this.logger.log(`EventStartDate: ${JSON.stringify(azureEvent.EventStartDate)}`);
            this.logger.log(`EventStartTime: ${JSON.stringify(azureEvent.EventStartTime)}`);
            this.logger.log(`EventEndDate: ${JSON.stringify(azureEvent.EventEndDate)}`);
            this.logger.log(`EventEndTime: ${JSON.stringify(azureEvent.EventEndTime)}`);
            let startDateTime = null;
            let endDateTime = null;
            let startDateTimeStr = null;
            let endDateTimeStr = null;
            if (azureEvent.EventStartDate) {
                const startDateStr = azureEvent.EventStartDate.toISOString().split('T')[0];
                let startTimeStr = '00:00:00';
                if (azureEvent.EventStartTime) {
                    this.logger.log(`Raw EventStartTime: ${JSON.stringify(azureEvent.EventStartTime)}`);
                    if (typeof azureEvent.EventStartTime === 'string') {
                        startTimeStr = azureEvent.EventStartTime.substring(0, 8);
                        this.logger.log(`Using EventStartTime string directly: ${startTimeStr}`);
                    }
                    else {
                        startTimeStr = azureEvent.EventStartTime.toTimeString().substring(0, 8);
                        this.logger.log(`Extracted time from Date object: ${startTimeStr}`);
                    }
                }
                startDateTimeStr = `${startDateStr} ${startTimeStr}`;
                this.logger.log(`Processed merge start date/time: ${startDateTimeStr}`);
            }
            if (azureEvent.EventEndDate) {
                const endDateStr = azureEvent.EventEndDate.toISOString().split('T')[0];
                let endTimeStr = '00:00:00';
                if (azureEvent.EventEndTime) {
                    this.logger.log(`Raw EventEndTime: ${JSON.stringify(azureEvent.EventEndTime)}`);
                    if (typeof azureEvent.EventEndTime === 'string') {
                        endTimeStr = azureEvent.EventEndTime.substring(0, 8);
                        this.logger.log(`Using EventEndTime string directly: ${endTimeStr}`);
                    }
                    else {
                        endTimeStr = azureEvent.EventEndTime.toTimeString().substring(0, 8);
                        this.logger.log(`Extracted time from Date object: ${endTimeStr}`);
                    }
                }
                endDateTimeStr = `${endDateStr} ${endTimeStr}`;
                this.logger.log(`Processed merge end date/time: ${endDateTimeStr}`);
            }
            else if (azureEvent.EventStartDate && azureEvent.EventEndTime) {
                const endDateStr = azureEvent.EventStartDate.toISOString().split('T')[0];
                let endTimeStr = '00:00:00';
                if (typeof azureEvent.EventEndTime === 'string') {
                    endTimeStr = azureEvent.EventEndTime.substring(0, 8);
                }
                else {
                    endTimeStr = azureEvent.EventEndTime.toTimeString().substring(0, 8);
                }
                endDateTimeStr = `${endDateStr} ${endTimeStr}`;
                this.logger.log(`Processed merge end date/time (using start date): ${endDateTimeStr}`);
            }
            let imageUrl = '';
            let mediaJson = null;
            if (azureEvent.ProfileImageCropperValue && typeof azureEvent.ProfileImageCropperValue === 'string') {
                try {
                    const imageData = JSON.parse(azureEvent.ProfileImageCropperValue);
                    mediaJson = azureEvent.ProfileImageCropperValue;
                    this.logger.log(`Using raw ProfileImageCropperValue for media column: ${mediaJson}`);
                    if (imageData && imageData.Src) {
                        const imagePath = imageData.Src;
                        imageUrl = imagePath.startsWith('http')
                            ? imagePath
                            : `https://tucsonlovesmusic.com${imagePath}`;
                        this.logger.log(`Extracted image URL: ${imageUrl}`);
                    }
                }
                catch (e) {
                    this.logger.error(`Error parsing event ProfileImageCropperValue JSON: ${e.message}`);
                }
            }
            else {
                this.logger.log('No ProfileImageCropperValue data found, will use empty object for media');
            }
            const rawStartTime = typeof azureEvent.EventStartTime === 'string' ?
                azureEvent.EventStartTime.substring(0, 8) :
                (azureEvent.EventStartTime ? azureEvent.EventStartTime.toTimeString().substring(0, 8) : '00:00:00');
            const rawEndTime = typeof azureEvent.EventEndTime === 'string' ?
                azureEvent.EventEndTime.substring(0, 8) :
                (azureEvent.EventEndTime ? azureEvent.EventEndTime.toTimeString().substring(0, 8) : '00:00:00');
            const datePart = azureEvent.EventStartDate.toISOString().split('T')[0];
            const correctedStartDateTime = `${datePart} ${rawStartTime}`;
            const correctedEndDateTime = `${datePart} ${rawEndTime}`;
            this.logger.log(`Corrected start date/time with original time: ${correctedStartDateTime}`);
            this.logger.log(`Corrected end date/time with original time: ${correctedEndDateTime}`);
            return {
                Id: azureEvent.Id,
                Name: azureEvent.Name || 'Unknown Event',
                Description: azureEvent.Description || '',
                StartDateTime: correctedStartDateTime,
                EndDateTime: correctedEndDateTime,
                StartDateTimeStr: correctedStartDateTime,
                EndDateTimeStr: correctedEndDateTime,
                VenueId: azureEvent.VenueId,
                ImageUrl: imageUrl,
                Media: mediaJson || '{}',
                Featured: azureEvent.FeatureLevel > 0 ? (azureEvent.FeatureLevel / 100) : 0.00,
                FeaturedOrder: azureEvent.FeatureLevel,
                Url: azureEvent.Url,
                CreatedAt: azureEvent.CreatedOn ? new Date(azureEvent.CreatedOn).toISOString() : new Date().toISOString(),
                UpdatedAt: azureEvent.UpdatedOn ? new Date(azureEvent.UpdatedOn).toISOString() : new Date().toISOString()
            };
        }
        catch (error) {
            this.logger.error(`Error fetching Azure event data: ${error.message}`);
            return null;
        }
        finally {
            if (pool) {
                await pool.close();
            }
        }
    }
    async getMissingAzureCounts() {
        try {
            this.logger.log('Getting counts of records missing Azure IDs...');
            const musiciansMissingAzure = await this.dataSource.query(`
        SELECT COUNT(*) as count
        FROM talent 
        WHERE azure_id IS NULL OR azure_id = ''
      `);
            const venuesMissingAzure = await this.dataSource.query(`
        SELECT COUNT(*) as count
        FROM venue 
        WHERE azure_id IS NULL OR azure_id = ''
      `);
            const eventsMissingAzure = await this.dataSource.query(`
        SELECT COUNT(*) as count
        FROM event 
        WHERE azure_id IS NULL OR azure_id = ''
      `);
            const counts = {
                musicians: parseInt(musiciansMissingAzure[0]?.count || '0'),
                venues: parseInt(venuesMissingAzure[0]?.count || '0'),
                events: parseInt(eventsMissingAzure[0]?.count || '0')
            };
            this.logger.log(`Missing Azure ID counts: ${JSON.stringify(counts)}`);
            return counts;
        }
        catch (error) {
            this.logger.error(`Error getting missing Azure ID counts: ${error.message}`);
            throw new Error('Failed to get missing Azure ID counts');
        }
    }
    async getMissingAzureMusicians() {
        try {
            this.logger.log('Getting musicians missing Azure IDs...');
            const musicians = await this.dataSource.query(`
        SELECT 
          t.id,
          t.name,
          t.created_at as "createdAt",
          t.updated_at as "updatedAt",
          t.image_url,
          COUNT(et.event_id) as "eventCount"
        FROM talent t
        LEFT JOIN event_talents et ON t.id = et.talent_id
        WHERE t.azure_id IS NULL OR t.azure_id = ''
        GROUP BY t.id, t.name, t.created_at, t.updated_at, t.image_url
        ORDER BY "eventCount" DESC, t.updated_at DESC
        LIMIT 50
      `);
            const musiciansWithMatches = await Promise.all(musicians.map(async (musician) => {
                const potentialMatches = await this.findPotentialAzureMatches(musician.name, 'musicians');
                return {
                    id: musician.id,
                    name: musician.name,
                    createdAt: musician.createdAt,
                    updatedAt: musician.updatedAt,
                    eventCount: parseInt(musician.eventCount || '0'),
                    hasImage: !!musician.image_url,
                    potentialMatches
                };
            }));
            this.logger.log(`Found ${musiciansWithMatches.length} musicians missing Azure IDs`);
            return musiciansWithMatches;
        }
        catch (error) {
            this.logger.error(`Error getting musicians missing Azure IDs: ${error.message}`);
            throw new Error('Failed to get musicians missing Azure IDs');
        }
    }
    async linkAzureId(type, data) {
        try {
            this.logger.log(`Linking Azure ID ${data.azureId} to ${type} record ${data.recordId}`);
            let tableName;
            let idColumn;
            switch (type) {
                case 'musicians':
                    tableName = 'talent';
                    idColumn = 'azure_id';
                    break;
                case 'venues':
                    tableName = 'venues';
                    idColumn = 'azure_id';
                    break;
                case 'events':
                    tableName = 'events';
                    idColumn = 'azure_id';
                    break;
                default:
                    throw new Error(`Invalid type: ${type}`);
            }
            const result = await this.dataSource.query(`UPDATE ${tableName} SET ${idColumn} = $1 WHERE id = $2`, [data.azureId, data.recordId]);
            if (result.affectedRows === 0) {
                throw new Error(`No ${type} record found with ID: ${data.recordId}`);
            }
            this.logger.log(`Successfully linked Azure ID ${data.azureId} to ${type} record ${data.recordId}`);
            return {
                success: true,
                message: `Azure ID linked successfully to ${type} record`
            };
        }
        catch (error) {
            this.logger.error(`Error linking Azure ID: ${error.message}`);
            throw new Error(`Failed to link Azure ID: ${error.message}`);
        }
    }
    async findPotentialAzureMatches(name, type) {
        try {
            const pool = await this.checkAzureDbConnection();
            let query;
            let nameColumn;
            let idColumn;
            switch (type) {
                case 'musicians':
                    query = 'SELECT Id, Name FROM Talents WHERE Name IS NOT NULL';
                    nameColumn = 'Name';
                    idColumn = 'Id';
                    break;
                case 'venues':
                    query = 'SELECT Id, Name FROM Venues WHERE Name IS NOT NULL';
                    nameColumn = 'Name';
                    idColumn = 'Id';
                    break;
                case 'events':
                    query = 'SELECT Id, Name FROM Events WHERE Name IS NOT NULL';
                    nameColumn = 'Name';
                    idColumn = 'Id';
                    break;
                default:
                    return [];
            }
            const result = await pool.request().query(query);
            const azureRecords = result.recordset;
            const matches = [];
            const searchName = name.toLowerCase().trim();
            for (const record of azureRecords) {
                const azureName = record[nameColumn]?.toLowerCase().trim();
                if (!azureName)
                    continue;
                let confidence = 0;
                let matchType = 'fuzzy';
                if (azureName === searchName) {
                    confidence = 1.0;
                    matchType = 'exact';
                }
                else if (azureName.includes(searchName) || searchName.includes(azureName)) {
                    const longer = azureName.length > searchName.length ? azureName : searchName;
                    const shorter = azureName.length <= searchName.length ? azureName : searchName;
                    confidence = shorter.length / longer.length;
                    matchType = 'fuzzy';
                }
                else {
                    const searchWords = searchName.split(/\s+/);
                    const azureWords = azureName.split(/\s+/);
                    const commonWords = searchWords.filter(word => azureWords.some(azureWord => azureWord.includes(word) || word.includes(azureWord)));
                    if (commonWords.length > 0) {
                        confidence = commonWords.length / Math.max(searchWords.length, azureWords.length);
                        matchType = 'fuzzy';
                    }
                }
                if (confidence >= 0.3) {
                    matches.push({
                        azureId: record[idColumn],
                        azureName: record[nameColumn],
                        confidence,
                        matchType
                    });
                }
            }
            matches.sort((a, b) => b.confidence - a.confidence);
            await pool.close();
            return matches.slice(0, 5);
        }
        catch (error) {
            this.logger.error(`Error finding potential Azure matches: ${error.message}`);
            return [];
        }
    }
};
exports.IdValidationController = IdValidationController;
__decorate([
    (0, common_1.Get)('issues/events'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], IdValidationController.prototype, "getEventIssues", null);
__decorate([
    (0, common_1.Post)('batch-fix-events'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], IdValidationController.prototype, "batchFixEvents", null);
__decorate([
    (0, common_1.Get)('counts'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], IdValidationController.prototype, "getValidationCounts", null);
__decorate([
    (0, common_1.Get)('azure-records/:entityType/:azureId'),
    __param(0, (0, common_1.Param)('entityType')),
    __param(1, (0, common_1.Param)('azureId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], IdValidationController.prototype, "getAzureRecordById", null);
__decorate([
    (0, common_1.Get)('issues/:type'),
    __param(0, (0, common_1.Param)('type')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], IdValidationController.prototype, "getValidationIssues", null);
__decorate([
    (0, common_1.Post)('update-azure-id'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], IdValidationController.prototype, "updateAzureId", null);
__decorate([
    (0, common_1.Post)('create-missing-event'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], IdValidationController.prototype, "createMissingEvent", null);
__decorate([
    (0, common_1.Post)('resolve/:issueId'),
    __param(0, (0, common_1.Param)('issueId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], IdValidationController.prototype, "resolveIssue", null);
__decorate([
    (0, common_1.Delete)(':entityType/:id'),
    __param(0, (0, common_1.Param)('entityType')),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], IdValidationController.prototype, "deleteRecord", null);
__decorate([
    (0, common_1.Get)('validation/missing-azure/counts'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], IdValidationController.prototype, "getMissingAzureCounts", null);
__decorate([
    (0, common_1.Get)('validation/missing-azure/musicians'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], IdValidationController.prototype, "getMissingAzureMusicians", null);
__decorate([
    (0, common_1.Post)('validation/missing-azure/:type/link'),
    __param(0, (0, common_1.Param)('type')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], IdValidationController.prototype, "linkAzureId", null);
exports.IdValidationController = IdValidationController = IdValidationController_1 = __decorate([
    (0, common_1.Controller)('admin/id-validation'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, typeorm_1.InjectDataSource)()),
    __metadata("design:paramtypes", [typeorm_2.DataSource])
], IdValidationController);
//# sourceMappingURL=id-validation.controller.js.map