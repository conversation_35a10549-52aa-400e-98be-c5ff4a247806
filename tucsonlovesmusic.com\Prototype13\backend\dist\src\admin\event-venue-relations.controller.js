"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventVenueRelationsController = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const auth_guard_1 = require("../auth/auth.guard");
const swagger_1 = require("@nestjs/swagger");
const sql = __importStar(require("mssql"));
const dotenv = __importStar(require("dotenv"));
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class UpdateEventVenueDto {
}
let EventVenueRelationsController = class EventVenueRelationsController {
    constructor(dataSource) {
        this.dataSource = dataSource;
        this.azurePoolPromise = null;
        this.connectionRetryAttempts = 3;
        this.connectionRetryDelay = 3000;
        dotenv.config();
        this.azureConfig = {
            server: process.env.AZURE_DB_SERVER || 'mssql.drv1.umbhost.net',
            database: process.env.AZURE_DB_NAME || 'TLM',
            user: process.env.AZURE_DB_USER || 'Reader',
            password: process.env.AZURE_DB_PASSWORD || 'TLM1234!',
            options: {
                encrypt: false,
                trustServerCertificate: true,
                enableArithAbort: true,
                connectTimeout: 30000,
                requestTimeout: 30000
            },
            pool: {
                max: 10,
                min: 1,
                idleTimeoutMillis: 30000,
                acquireTimeoutMillis: 15000
            }
        };
    }
    async getAzurePool() {
        if (this.azurePoolPromise) {
            try {
                const pool = await this.azurePoolPromise;
                if (pool.connected) {
                    try {
                        await pool.request().query('SELECT 1 AS testConnection');
                        return pool;
                    }
                    catch (queryError) {
                        console.warn('Azure SQL connection test failed:', queryError instanceof Error ? queryError.message : 'Unknown error');
                        await this.closeExistingPool();
                    }
                }
                else {
                    await this.closeExistingPool();
                }
            }
            catch (error) {
                console.error('Azure SQL pool access error:', error instanceof Error ? error.message : 'Unknown error');
                await this.closeExistingPool();
            }
        }
        return this.createNewPool();
    }
    async closeExistingPool() {
        if (this.azurePoolPromise) {
            try {
                const pool = await this.azurePoolPromise;
                if (pool && pool.connected) {
                    await pool.close();
                }
            }
            catch (error) {
                console.warn('Error closing Azure SQL pool:', error instanceof Error ? error.message : 'Unknown error');
            }
            finally {
                this.azurePoolPromise = null;
            }
        }
    }
    async createNewPool() {
        let retryCount = 0;
        let lastError = null;
        while (retryCount < this.connectionRetryAttempts) {
            try {
                this.azurePoolPromise = new sql.ConnectionPool(this.azureConfig).connect();
                const pool = await this.azurePoolPromise;
                await pool.request().query('SELECT 1 AS testConnection');
                console.log(`Successfully connected to Azure SQL (attempt ${retryCount + 1})`);
                return pool;
            }
            catch (error) {
                lastError = error;
                retryCount++;
                console.warn(`Azure SQL connection attempt ${retryCount} failed:`, error instanceof Error ? error.message : 'Unknown error');
                this.azurePoolPromise = null;
                if (retryCount < this.connectionRetryAttempts) {
                    await new Promise(resolve => setTimeout(resolve, this.connectionRetryDelay));
                }
            }
        }
        console.error('All Azure SQL connection attempts failed');
        throw lastError;
    }
    async getEventVenueStatusCounts() {
        let azurePool = null;
        try {
            azurePool = await this.getAzurePool();
            const totalEventsResult = await this.dataSource.query(`
        SELECT COUNT(*) as total FROM event WHERE deleted = false
      `);
            const totalEvents = parseInt(totalEventsResult[0].total);
            const missingVenuesResult = await this.dataSource.query(`
        SELECT COUNT(DISTINCT e.id) as count 
        FROM event e
        WHERE e.deleted = false
        AND e.azure_id IS NOT NULL
        AND (e.venue_id IS NULL)
      `);
            const missingVenues = parseInt(missingVenuesResult[0].count);
            const mismatchedResult = await this.dataSource.query(`
        SELECT COUNT(DISTINCT e.id) as count
        FROM event e
        JOIN venue v ON e.venue_id = v.id
        WHERE e.deleted = false 
        AND e.azure_id IS NOT NULL 
      `);
            const mismatchedVenues = parseInt(mismatchedResult[0].count);
            const potentialFixesResult = await this.dataSource.query(`
        SELECT COUNT(*) as count 
        FROM event e
        WHERE e.deleted = false
        AND e.azure_id IS NOT NULL
        AND e.azure_id != ''
      `);
            const potentialFixes = parseInt(potentialFixesResult[0].count);
            return {
                total: totalEvents,
                missingVenues,
                mismatchedVenues,
                potentialFixes
            };
        }
        catch (error) {
            console.error('Error in getEventVenueStatusCounts:', error instanceof Error ? error.message : 'Unknown error');
            if (error instanceof Error && error.message.includes('Connection is closed')) {
                console.warn('Connection issue detected, will retry on next request');
                await this.closeExistingPool();
            }
            throw error;
        }
    }
    async getEventsWithMissingVenues() {
        let azurePool = null;
        try {
            azurePool = await this.getAzurePool();
            const query = `
        SELECT 
          e.id, 
          e.name, 
          e.azure_id,
          e."startDateTime" as date,
          e.venue_id as postgres_venue_id,
          v.name as postgres_venue_name
        FROM event e
        LEFT JOIN venue v ON e.venue_id = v.id
        WHERE e.deleted = false
        AND e.azure_id IS NOT NULL
        AND (e.venue_id IS NULL)
        ORDER BY e."startDateTime" DESC
        LIMIT 100
      `;
            const events = await this.dataSource.query(query);
            const results = [];
            for (const event of events) {
                const azureResult = await azurePool.request()
                    .input('eventId', sql.UniqueIdentifier, event.azure_id)
                    .query(`
            SELECT v.Id as venueId, v.Name as venueName
            FROM PerformanceEvents pe
            JOIN Venues v ON pe.VenueId = v.Id
            WHERE pe.Id = @eventId
          `);
                if (azureResult.recordset.length > 0) {
                    const azureVenue = azureResult.recordset[0];
                    results.push({
                        id: event.id,
                        name: event.name,
                        date: event.date,
                        azure_venue_id: azureVenue.venueId,
                        azure_venue_name: azureVenue.venueName,
                        postgres_venue_id: event.postgres_venue_id,
                        postgres_venue_name: event.postgres_venue_name
                    });
                }
            }
            return results;
        }
        catch (error) {
            console.error('Error in getEventsWithMissingVenues:', error instanceof Error ? error.message : 'Unknown error');
            if (error instanceof Error && error.message.includes('Connection is closed')) {
                console.warn('Connection issue detected, will retry on next request');
                await this.closeExistingPool();
            }
            throw error;
        }
    }
    async getEventsWithMismatchedVenues() {
        let azurePool = null;
        try {
            azurePool = await this.getAzurePool();
            const query = `
        SELECT 
          e.id, 
          e.name, 
          e.azure_id,
          e."startDateTime" as date,
          e.venue_id as postgres_venue_id,
          v.name as postgres_venue_name,
          v.azure_id as postgres_venue_azure_id
        FROM event e
        JOIN venue v ON e.venue_id = v.id
        WHERE e.deleted = false
        AND e.azure_id IS NOT NULL
        ORDER BY e."startDateTime" DESC
        LIMIT 100
      `;
            const events = await this.dataSource.query(query);
            const results = [];
            for (const event of events) {
                const azureResult = await azurePool.request()
                    .input('eventId', sql.UniqueIdentifier, event.azure_id)
                    .query(`
            SELECT v.Id as venueId, v.Name as venueName
            FROM PerformanceEvents pe
            JOIN Venues v ON pe.VenueId = v.Id
            WHERE pe.Id = @eventId
          `);
                if (azureResult.recordset.length > 0) {
                    const azureVenue = azureResult.recordset[0];
                    const azureVenueId = azureVenue.venueId.toLowerCase();
                    const postgresVenueAzureId = event.postgres_venue_azure_id ? event.postgres_venue_azure_id.toLowerCase() : null;
                    const azureVenueName = azureVenue.venueName.trim().toLowerCase();
                    const postgresVenueName = event.postgres_venue_name ? event.postgres_venue_name.trim().toLowerCase() : '';
                    if ((!postgresVenueAzureId || azureVenueId !== postgresVenueAzureId) &&
                        azureVenueName !== postgresVenueName) {
                        results.push({
                            id: event.id,
                            name: event.name,
                            date: event.date,
                            azure_venue_id: azureVenue.venueId,
                            azure_venue_name: azureVenue.venueName,
                            postgres_venue_id: event.postgres_venue_id,
                            postgres_venue_name: event.postgres_venue_name
                        });
                    }
                }
            }
            return results;
        }
        catch (error) {
            console.error('Error in getEventsWithMismatchedVenues:', error instanceof Error ? error.message : 'Unknown error');
            if (error instanceof Error && error.message.includes('Connection is closed')) {
                console.warn('Connection issue detected, will retry on next request');
                await this.closeExistingPool();
            }
            throw error;
        }
    }
    async fixEventVenueRelationship(updateDto) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const { eventId, venueId } = updateDto;
            let azurePool = null;
            azurePool = await this.getAzurePool();
            const azureVenueResult = await azurePool.request()
                .input('venueId', sql.UniqueIdentifier, venueId)
                .query(`
          SELECT Id, Name 
          FROM Venues 
          WHERE Id = @venueId
        `);
            if (azureVenueResult.recordset.length === 0) {
                throw new Error(`Venue with ID ${venueId} not found in Azure SQL`);
            }
            const azureVenue = azureVenueResult.recordset[0];
            const existingVenueQuery = `
        SELECT id 
        FROM venue 
        WHERE azure_id = $1
      `;
            const existingVenueResult = await queryRunner.query(existingVenueQuery, [venueId]);
            let postgresVenueId;
            if (existingVenueResult.length === 0) {
                const generateSlug = (name) => {
                    return name
                        .toLowerCase()
                        .replace(/[^a-z0-9]+/g, '-')
                        .replace(/^-+|-+$/g, '')
                        .substring(0, 100)
                        + '-' + Date.now().toString().slice(-6);
                };
                const venueSlug = generateSlug(azureVenue.Name);
                const insertVenueQuery = `
          INSERT INTO venue (name, azure_id, slug, created_at, updated_at) 
          VALUES ($1, $2, $3, NOW(), NOW()) 
          RETURNING id
        `;
                const newVenueResult = await queryRunner.query(insertVenueQuery, [
                    azureVenue.Name,
                    azureVenue.Id,
                    venueSlug
                ]);
                postgresVenueId = newVenueResult[0].id;
            }
            else {
                postgresVenueId = existingVenueResult[0].id;
            }
            const updateEventQuery = `
        UPDATE event 
        SET venue_id = $1, updated_at = NOW() 
        WHERE id = $2
      `;
            await queryRunner.query(updateEventQuery, [postgresVenueId, eventId]);
            await queryRunner.commitTransaction();
            return { success: true, message: 'Event venue relationship fixed successfully' };
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            console.error('Error fixing event venue relationship:', error instanceof Error ? error.message : 'Unknown error');
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async fixAllEventVenueRelationships() {
        let azurePool = null;
        let fixedCount = 0;
        let errorCount = 0;
        try {
            azurePool = await this.getAzurePool();
            const query = `
        SELECT 
          e.id, 
          e.azure_id
        FROM event e
        WHERE e.deleted = false
        AND e.azure_id IS NOT NULL
        AND (e.venue_id IS NULL OR e.venue_id = '')
        ORDER BY e."startDateTime" DESC
        LIMIT 500
      `;
            const events = await this.dataSource.query(query);
            for (const event of events) {
                const queryRunner = this.dataSource.createQueryRunner();
                await queryRunner.connect();
                await queryRunner.startTransaction();
                try {
                    const azureResult = await azurePool.request()
                        .input('eventId', sql.UniqueIdentifier, event.azure_id)
                        .query(`
              SELECT v.Id as venueId, v.Name as venueName
              FROM PerformanceEvents pe
              JOIN Venues v ON pe.VenueId = v.Id
              WHERE pe.Id = @eventId
            `);
                    if (azureResult.recordset.length > 0) {
                        const azureVenue = azureResult.recordset[0];
                        const existingVenueQuery = `
              SELECT id 
              FROM venue 
              WHERE azure_id = $1
            `;
                        const existingVenueResult = await queryRunner.query(existingVenueQuery, [azureVenue.venueId]);
                        let postgresVenueId;
                        if (existingVenueResult.length === 0) {
                            const generateSlug = (name) => {
                                return name
                                    .toLowerCase()
                                    .replace(/[^a-z0-9]+/g, '-')
                                    .replace(/^-+|-+$/g, '')
                                    .substring(0, 100)
                                    + '-' + Date.now().toString().slice(-6);
                            };
                            const venueSlug = generateSlug(azureVenue.venueName);
                            const insertVenueQuery = `
                INSERT INTO venue (name, azure_id, slug, created_at, updated_at) 
                VALUES ($1, $2, $3, NOW(), NOW()) 
                RETURNING id
              `;
                            const newVenueResult = await queryRunner.query(insertVenueQuery, [
                                azureVenue.venueName,
                                azureVenue.venueId,
                                venueSlug
                            ]);
                            postgresVenueId = newVenueResult[0].id;
                        }
                        else {
                            postgresVenueId = existingVenueResult[0].id;
                        }
                        const updateEventQuery = `
              UPDATE event 
              SET venue_id = $1, updated_at = NOW() 
              WHERE id = $2
            `;
                        await queryRunner.query(updateEventQuery, [postgresVenueId, event.id]);
                        await queryRunner.commitTransaction();
                        fixedCount++;
                    }
                }
                catch (error) {
                    await queryRunner.rollbackTransaction();
                    console.error(`Error fixing event ${event.id}:`, error instanceof Error ? error.message : 'Unknown error');
                    errorCount++;
                }
                finally {
                    await queryRunner.release();
                }
            }
            return {
                success: true,
                message: `Fixed ${fixedCount} event-venue relationships${errorCount > 0 ? ` (${errorCount} errors)` : ''}`,
                fixed: fixedCount,
                errors: errorCount
            };
        }
        catch (error) {
            console.error('Error in bulk fix operation:', error instanceof Error ? error.message : 'Unknown error');
            throw error;
        }
    }
    async fixEventVenueRelationshipsFromCsv() {
        console.log('🔧 [Backend] Starting CSV-based event-venue relationship fix with Azure validation...');
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        let transactionStarted = false;
        let azurePool = null;
        try {
            await queryRunner.startTransaction();
            transactionStarted = true;
            console.log('✅ [Backend] Transaction started');
            console.log('🔗 [Backend] Connecting to Azure MSSQL for validation...');
            azurePool = await this.getAzurePool();
            console.log('✅ [Backend] Connected to Azure MSSQL successfully');
            const csvPath = path.join(process.cwd(), 'data/imports/venuejoins.csv');
            console.log('📁 [Backend] Reading CSV from:', csvPath);
            const csvContent = fs.readFileSync(csvPath, 'utf-8');
            const lines = csvContent.trim().split('\n');
            const header = lines[0];
            const dataLines = lines.slice(1);
            console.log(`📊 [Backend] CSV contains ${dataLines.length} relationships`);
            const csvRelationships = dataLines.map((line, index) => {
                const [eventGuid, venueGuid] = line.split(',').map(id => id.trim());
                const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
                if (!guidRegex.test(eventGuid) || !guidRegex.test(venueGuid)) {
                    console.warn(`⚠️ [Backend] Invalid GUID format at line ${index + 2}: Event=${eventGuid}, Venue=${venueGuid}`);
                    return null;
                }
                return {
                    eventGuid: eventGuid.toLowerCase(),
                    venueGuid: venueGuid.toLowerCase()
                };
            }).filter(rel => rel !== null);
            console.log(`✅ [Backend] Parsed ${csvRelationships.length} valid GUID relationships`);
            console.log('🔍 [Backend] Validating relationships against Azure MSSQL source...');
            const validatedRelationships = [];
            let invalidEventCount = 0;
            let invalidVenueCount = 0;
            let validRelationshipCount = 0;
            const uniqueEventIds = [...new Set(csvRelationships.map(r => r.eventGuid))];
            const uniqueVenueIds = [...new Set(csvRelationships.map(r => r.venueGuid))];
            console.log(`📊 [Backend] Validating ${uniqueEventIds.length} unique events and ${uniqueVenueIds.length} unique venues...`);
            const validEventIds = new Set();
            const eventBatchSize = 100;
            for (let i = 0; i < uniqueEventIds.length; i += eventBatchSize) {
                const batch = uniqueEventIds.slice(i, i + eventBatchSize);
                const placeholders = batch.map((_, index) => `@eventId${index}`).join(',');
                const request = azurePool.request();
                batch.forEach((eventId, index) => {
                    request.input(`eventId${index}`, sql.UniqueIdentifier, eventId);
                });
                const eventResult = await request.query(`
          SELECT Id FROM PerformanceEvents WHERE Id IN (${placeholders})
        `);
                eventResult.recordset.forEach(row => {
                    validEventIds.add(row.Id.toLowerCase());
                });
            }
            const validVenueIds = new Set();
            const venueBatchSize = 100;
            for (let i = 0; i < uniqueVenueIds.length; i += venueBatchSize) {
                const batch = uniqueVenueIds.slice(i, i + venueBatchSize);
                const placeholders = batch.map((_, index) => `@venueId${index}`).join(',');
                const request = azurePool.request();
                batch.forEach((venueId, index) => {
                    request.input(`venueId${index}`, sql.UniqueIdentifier, venueId);
                });
                const venueResult = await request.query(`
          SELECT Id FROM Venues WHERE Id IN (${placeholders})
        `);
                venueResult.recordset.forEach(row => {
                    validVenueIds.add(row.Id.toLowerCase());
                });
            }
            const validRelationshipKeys = new Set();
            const relationshipBatchSize = 50;
            for (let i = 0; i < csvRelationships.length; i += relationshipBatchSize) {
                const batch = csvRelationships.slice(i, i + relationshipBatchSize);
                const conditions = batch.map((_, index) => `(pe.Id = @eventId${index} AND pe.VenueId = @venueId${index})`).join(' OR ');
                const request = azurePool.request();
                batch.forEach((rel, index) => {
                    request.input(`eventId${index}`, sql.UniqueIdentifier, rel.eventGuid);
                    request.input(`venueId${index}`, sql.UniqueIdentifier, rel.venueGuid);
                });
                const relationshipResult = await request.query(`
          SELECT pe.Id as EventId, pe.VenueId
          FROM PerformanceEvents pe
          WHERE ${conditions}
        `);
                relationshipResult.recordset.forEach(row => {
                    const key = `${row.EventId.toLowerCase()}-${row.VenueId.toLowerCase()}`;
                    validRelationshipKeys.add(key);
                });
            }
            console.log(`✅ [Backend] Batch validation complete - found ${validEventIds.size} valid events, ${validVenueIds.size} valid venues, ${validRelationshipKeys.size} valid relationships`);
            for (const relationship of csvRelationships) {
                const eventId = relationship.eventGuid.toLowerCase();
                const venueId = relationship.venueGuid.toLowerCase();
                const relationshipKey = `${eventId}-${venueId}`;
                if (!validEventIds.has(eventId)) {
                    invalidEventCount++;
                    continue;
                }
                if (!validVenueIds.has(venueId)) {
                    invalidVenueCount++;
                    continue;
                }
                if (!validRelationshipKeys.has(relationshipKey)) {
                    continue;
                }
                validatedRelationships.push(relationship);
                validRelationshipCount++;
            }
            console.log(`📊 [Backend] Validation summary:`);
            console.log(`   - Valid relationships: ${validRelationshipCount}`);
            console.log(`   - Invalid events: ${invalidEventCount}`);
            console.log(`   - Invalid venues: ${invalidVenueCount}`);
            return {
                success: true,
                message: `CSV validation completed successfully`,
                stats: {
                    totalCsvRelationships: csvRelationships.length,
                    validatedRelationships: validRelationshipCount,
                    invalidEvents: invalidEventCount,
                    invalidVenues: invalidVenueCount,
                    validEvents: validEventIds.size,
                    validVenues: validVenueIds.size
                }
            };
        }
        catch (error) {
            console.error('🔧 [Backend] Error in CSV-based venue relationship fix:', error);
            if (transactionStarted) {
                try {
                    await queryRunner.rollbackTransaction();
                    console.log('🔄 [Backend] Transaction rolled back due to error');
                }
                catch (rollbackError) {
                    console.error('🔧 [Backend] Error rolling back transaction:', rollbackError);
                }
            }
            throw error;
        }
        finally {
            if (queryRunner) {
                try {
                    await queryRunner.release();
                    console.log('🔄 [Backend] Query runner released');
                }
                catch (releaseError) {
                    console.error('🔧 [Backend] Error releasing query runner:', releaseError);
                }
            }
        }
    }
};
exports.EventVenueRelationsController = EventVenueRelationsController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EventVenueRelationsController.prototype, "getEventVenueStatusCounts", null);
__decorate([
    (0, common_1.Get)('missing'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EventVenueRelationsController.prototype, "getEventsWithMissingVenues", null);
__decorate([
    (0, common_1.Get)('mismatched'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EventVenueRelationsController.prototype, "getEventsWithMismatchedVenues", null);
__decorate([
    (0, common_1.Post)('fix'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [UpdateEventVenueDto]),
    __metadata("design:returntype", Promise)
], EventVenueRelationsController.prototype, "fixEventVenueRelationship", null);
__decorate([
    (0, common_1.Post)('fix-all'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EventVenueRelationsController.prototype, "fixAllEventVenueRelationships", null);
__decorate([
    (0, common_1.Post)('fix-from-csv'),
    (0, swagger_1.ApiOperation)({ summary: 'Fix event-venue relationships using CSV data with Azure MSSQL validation' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Event-venue relationships fixed successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EventVenueRelationsController.prototype, "fixEventVenueRelationshipsFromCsv", null);
exports.EventVenueRelationsController = EventVenueRelationsController = __decorate([
    (0, common_1.Controller)('admin/event-venue-relations'),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, typeorm_1.InjectDataSource)()),
    __metadata("design:paramtypes", [typeorm_2.DataSource])
], EventVenueRelationsController);
//# sourceMappingURL=event-venue-relations.controller.js.map