import { DataSource } from 'typeorm';
interface ValidationCounts {
    venueCount: number;
    musicianCount: number;
    eventCount: number;
}
interface ValidationIssue {
    id: string;
    entityType: 'venues' | 'musicians' | 'events';
    postgresRecord?: any;
    azureRecord?: any;
    issueType: 'missing_azure' | 'missing_postgres' | 'id_mismatch' | 'data_mismatch';
    description: string;
    createdAt: string;
}
interface FixIssueDto {
    issueId: string;
    resolution: 'fix' | 'merge';
    matchId?: string;
    azureId?: string;
    mergeFields?: boolean;
    issueType?: 'missing_azure' | 'missing_postgres' | 'id_mismatch' | 'data_mismatch';
    entityType?: 'venues' | 'musicians' | 'events';
    azureRecord?: any;
}
export declare class IdValidationController {
    private dataSource;
    private readonly logger;
    private readonly columnDetector;
    constructor(dataSource: DataSource);
    private combineDateTime;
    private checkAzureDbConnection;
    getEventIssues(): Promise<ValidationIssue[]>;
    private processEventDates;
    batchFixEvents(): Promise<{
        totalIssues: number;
        processedCount: number;
        successCount: number;
        errorCount: number;
        errors: any[];
        fixedEvents: any[];
    }>;
    getValidationCounts(): Promise<ValidationCounts>;
    getAzureRecordById(entityType: string, azureId: string): Promise<{
        id: any;
        name: any;
        description: any;
        startDateTime: string;
        endDateTime: string;
        venueId: any;
        imageUrl: string;
        featured: boolean;
        url: any;
        Id?: undefined;
        Name?: undefined;
        Address?: undefined;
        City?: undefined;
        State?: undefined;
        Zip?: undefined;
        Website?: undefined;
        GooglePlaceId?: undefined;
        Latitude?: undefined;
        Longitude?: undefined;
        CreatedAt?: undefined;
        UpdatedAt?: undefined;
        Locale?: undefined;
        Bio?: undefined;
        Category?: undefined;
        Genre?: undefined;
        ContactName?: undefined;
        Email?: undefined;
        PhoneNumber?: undefined;
        ImageUrl?: undefined;
        Url?: undefined;
        ImageData?: undefined;
        ProfileImageId?: undefined;
        Description?: undefined;
        StartDateTime?: undefined;
        EndDateTime?: undefined;
        VenueId?: undefined;
        ContactEmail?: undefined;
        ContactPhone?: undefined;
        ContactWebsite?: undefined;
        FeatureLevel?: undefined;
        CropData?: undefined;
    } | {
        Id: any;
        Name: any;
        Address: string;
        City: string;
        State: string;
        Zip: string;
        Website: any;
        GooglePlaceId: string;
        Latitude: any;
        Longitude: any;
        CreatedAt: string;
        UpdatedAt: string;
        Locale: any;
        id?: undefined;
        name?: undefined;
        description?: undefined;
        startDateTime?: undefined;
        endDateTime?: undefined;
        venueId?: undefined;
        imageUrl?: undefined;
        featured?: undefined;
        url?: undefined;
        Bio?: undefined;
        Category?: undefined;
        Genre?: undefined;
        ContactName?: undefined;
        Email?: undefined;
        PhoneNumber?: undefined;
        ImageUrl?: undefined;
        Url?: undefined;
        ImageData?: undefined;
        ProfileImageId?: undefined;
        Description?: undefined;
        StartDateTime?: undefined;
        EndDateTime?: undefined;
        VenueId?: undefined;
        ContactEmail?: undefined;
        ContactPhone?: undefined;
        ContactWebsite?: undefined;
        FeatureLevel?: undefined;
        CropData?: undefined;
    } | {
        Id: any;
        Name: any;
        Bio: any;
        Category: any;
        Genre: string;
        ContactName: any;
        Email: any;
        PhoneNumber: any;
        Website: any;
        ImageUrl: string;
        Url: any;
        CreatedAt: string;
        UpdatedAt: string;
        ImageData: any;
        ProfileImageId: any;
        id?: undefined;
        name?: undefined;
        description?: undefined;
        startDateTime?: undefined;
        endDateTime?: undefined;
        venueId?: undefined;
        imageUrl?: undefined;
        featured?: undefined;
        url?: undefined;
        Address?: undefined;
        City?: undefined;
        State?: undefined;
        Zip?: undefined;
        GooglePlaceId?: undefined;
        Latitude?: undefined;
        Longitude?: undefined;
        Locale?: undefined;
        Description?: undefined;
        StartDateTime?: undefined;
        EndDateTime?: undefined;
        VenueId?: undefined;
        ContactEmail?: undefined;
        ContactPhone?: undefined;
        ContactWebsite?: undefined;
        FeatureLevel?: undefined;
        CropData?: undefined;
    } | {
        Id: any;
        Name: any;
        Description: any;
        StartDateTime: string;
        EndDateTime: string;
        VenueId: any;
        ContactName: any;
        ContactEmail: any;
        ContactPhone: any;
        ContactWebsite: any;
        FeatureLevel: any;
        ImageUrl: string;
        ProfileImageId: any;
        CropData: any;
        CreatedAt: string;
        UpdatedAt: string;
        id?: undefined;
        name?: undefined;
        description?: undefined;
        startDateTime?: undefined;
        endDateTime?: undefined;
        venueId?: undefined;
        imageUrl?: undefined;
        featured?: undefined;
        url?: undefined;
        Address?: undefined;
        City?: undefined;
        State?: undefined;
        Zip?: undefined;
        Website?: undefined;
        GooglePlaceId?: undefined;
        Latitude?: undefined;
        Longitude?: undefined;
        Locale?: undefined;
        Bio?: undefined;
        Category?: undefined;
        Genre?: undefined;
        Email?: undefined;
        PhoneNumber?: undefined;
        Url?: undefined;
        ImageData?: undefined;
    }>;
    getValidationIssues(entityType: string): Promise<ValidationIssue[]>;
    updateAzureId(data: {
        tableName: string;
        recordId: string;
        azureId: string;
        mergeFields?: boolean;
    }): Promise<{
        success: boolean;
        message: string;
        entity: any;
    }>;
    createMissingEvent(data: {
        azureId: string;
    }): Promise<{
        success: boolean;
        message: string;
        postgresId: any;
        azureId: any;
        venueName: string;
        venueId: any;
        action: string;
    } | {
        success: boolean;
        message: string;
        postgresId: any;
        azureId: any;
        action: string;
        venueName?: undefined;
        venueId?: undefined;
    }>;
    resolveIssue(issueId: string, fixDto: FixIssueDto): Promise<{
        success: boolean;
        message: string;
    }>;
    private generateMockAzureId;
    private sanitizeForSql;
    private getMissingEvents;
    private fetchAzureTalentForMerge;
    private fetchAzureVenueForMerge;
    deleteRecord(entityType: string, id: string): Promise<{
        success: boolean;
        message: string;
        id: string;
    }>;
    private fetchAzureEventForMerge;
    getMissingAzureCounts(): Promise<{
        musicians: number;
        venues: number;
        events: number;
    }>;
    getMissingAzureMusicians(): Promise<any[]>;
    linkAzureId(type: 'musicians' | 'venues' | 'events', data: {
        recordId: string;
        azureId: string;
    }): Promise<{
        success: boolean;
        message: string;
    }>;
    private findPotentialAzureMatches;
}
export {};
