import { Repository, DataSource } from 'typeorm';
import { EventSyncLog } from '../entities/event-sync-log.entity';
export declare class EventSyncService {
    private eventSyncLogRepository;
    private dataSource;
    private readonly logger;
    private migrationOrchestrator;
    constructor(eventSyncLogRepository: Repository<EventSyncLog>, dataSource: DataSource);
    runHourlySync(): Promise<void>;
    getLatestSync(): Promise<EventSyncLog>;
    getSyncHistory(limit?: number): Promise<EventSyncLog[]>;
    triggerManualSync(): Promise<EventSyncLog>;
    syncEventTalents(): Promise<EventSyncLog>;
    private triggerSync;
    fixEventDatetimes(): Promise<EventSyncLog>;
    fixEventImages(): Promise<EventSyncLog>;
}
