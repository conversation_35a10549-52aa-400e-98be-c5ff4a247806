# Event and Talent Association Documentation

## Overview
The `event_talents` is a junction table that establishes a many-to-many relationship between Events and Talents in PostgreSQL. This table allows multiple talents to be associated with multiple events, serving as the PostgreSQL equivalent of the MSSQL `PerformanceEventModelTalentModel` table.

## Table Structure

| Field Name | Data Type | Nullable | Description |
|-----------|-----------|----------|-------------|
| created_at | timestamp | NO | Timestamp when the relationship was created |
| event_id | uuid | NO | Foreign key referencing the Event |
| talent_id | int4 | NO | Foreign key referencing the Talent |

## Relationship Characteristics
- Composite Primary Key: Combination of `event_id` and `talent_id`
- Includes creation timestamp for audit trail
- Enables flexible talent-to-event associations
- Cascade delete behavior on both foreign keys

## Sample Data Insights
- Each row represents a unique talent-event pairing
- Some events have multiple talents
- Some talents appear in multiple events
- Timestamps track when relationships were established

## Data Relationship Visualization
```
Event (1) -----> (M) event_talents (M) -----> (1) Talent
```

## Sample SQL Queries

### List Talents for a Specific Event
```sql
SELECT t.name, t.slug, t.genre, e.name as EventName
FROM event_talents et
JOIN talent t ON et.talent_id = t.id
JOIN event e ON et.event_id = e.id
WHERE e.id = 'specific-event-uuid';
```

### Count Talents per Event
```sql
SELECT e.name as EventName, COUNT(et.talent_id) as TalentCount
FROM event e
LEFT JOIN event_talents et ON e.id = et.event_id
GROUP BY e.name, e.id
ORDER BY TalentCount DESC;
```

### Find All Events for a Specific Talent
```sql
SELECT e.name, e.startDateTime, e.endDateTime, v.name as venue_name
FROM event_talents et
JOIN event e ON et.event_id = e.id
LEFT JOIN venue v ON e.venue_id = v.id
WHERE et.talent_id = 123
ORDER BY e.startDateTime;
```

### Recent Talent-Event Relationships
```sql
SELECT t.name as TalentName, e.name as EventName, et.created_at
FROM event_talents et
JOIN talent t ON et.talent_id = t.id
JOIN event e ON et.event_id = e.id
WHERE et.created_at > NOW() - INTERVAL '30 days'
ORDER BY et.created_at DESC;
```

## Data Migration from MSSQL

### Source to Target Mapping
| MSSQL Field | PostgreSQL Field | Notes |
|-------------|------------------|-------|
| PerformanceEventsId | event_id | GUID converted to UUID |
| TalentListId | talent_id | GUID converted to auto-increment integer |
| N/A | created_at | Added timestamp for audit trail |

### Migration Process
1. Extract relationships from `PerformanceEventModelTalentModel`
2. Map MSSQL GUIDs to PostgreSQL IDs using lookup tables
3. Insert into `event_talents` with transformed IDs
4. Preserve referential integrity during migration

## Performance and Scalability
- Lightweight junction table with minimal overhead
- Indexed on both foreign key columns for efficient joins
- Composite primary key prevents duplicate relationships
- Cascade delete ensures data consistency
- Timestamp column enables audit trail and performance analysis

## TypeORM Entity Definition

```typescript
@Entity('event_talents')
export class EventTalent {
  @PrimaryColumn({ name: 'event_id', type: 'uuid' })
  eventId: string;

  @PrimaryColumn({ name: 'talent_id', type: 'integer' })
  talentId: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ManyToOne(() => Event, event => event.talents, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'event_id' })
  event: Event;

  @ManyToOne(() => Talent, talent => talent.events, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'talent_id' })
  talent: Talent;
}
```

## Constraints and Validations
- Both `event_id` and `talent_id` are required (NOT NULL)
- No duplicate talent-event combinations allowed (composite primary key)
- Referential integrity maintained through foreign key constraints
- Cascade delete behavior on both parent tables
- `created_at` automatically populated on insert

## Foreign Key Constraints

| Constraint Name | Type | Referenced Table | Referenced Column | On Delete |
|----------------|------|------------------|-------------------|-----------|
| FK_aa1fb1736f8c895408dc86859b0 | FOREIGN KEY | event | id | CASCADE |
| FK_494a97f31c6e52a2dc862cc1db5 | FOREIGN KEY | talent | id | CASCADE |

## Potential Future Enhancements
- Add optional metadata like performance order or set times
- Include role or type of talent participation (headliner, opener, etc.)
- Add performance duration or billing information
- Include contract or booking status metadata
- Add last modified timestamp for relationship updates

## Data Integrity Considerations
1. Ensure all referenced events and talents exist before creating relationships
2. Validate UUID format for event_id references
3. Check integer range for talent_id references
4. Monitor for orphaned relationships during data cleanup
5. Regular integrity checks for referential consistency
