import { Controller, Get, Post, UseGuards, Logger, Inject, Body } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { AdminGuard } from '../../auth/admin.guard';
import { AuthGuard } from '../../auth/auth.guard';
import { EventsService } from '../events.service';
import { Event } from '../events.entity';
import { DatabaseResetService } from '../../database/reset.service';
import { MigrationOrchestrator } from '../../database/migration-modules';
import { MigrationSummary } from '../../database/migration-modules';
import * as path from 'path';
import * as fs from 'fs';
import { spawn, exec } from 'child_process';
import { promisify } from 'util';
import * as sql from 'mssql';

interface EventDiagnosticsResponse {
  totalEvents: number;
  eventsWithImages: number;
  eventsWithoutImages: number;
  events: Array<{ id: string; name: string; imageUrl: string | null }>;
}

interface EventImageFixResponse {
  total: number;
  processed: number;
  success: number;
  skipped: number;
  errors: number;
}

@Controller('admin/events')
@UseGuards(AuthGuard, AdminGuard)
export class AdminEventsController {
  private readonly logger = new Logger(AdminEventsController.name);

  constructor(
    private readonly eventsService: EventsService,
    @InjectRepository(Event)
    private readonly eventRepository: Repository<Event>,
    private readonly dataSource: DataSource,
    private readonly databaseResetService: DatabaseResetService,
  ) {}

  private async createAzureConnection(): Promise<sql.ConnectionPool> {
    // Check if we have a connection string (Heroku environment)
    const connectionString = process.env.AZURE_SQL_CONNECTION_STRING;
    
    let azureConnection: sql.ConnectionPool;
    
    if (connectionString) {
      // Use connection string for Heroku deployment
      azureConnection = new sql.ConnectionPool(connectionString);
    } else {
      // Use individual environment variables for local development
      const azureConfig: sql.config = {
        user: process.env.AZURE_SQL_USER!,
        password: process.env.AZURE_SQL_PASSWORD!,
        server: process.env.AZURE_SQL_SERVER!,
        database: process.env.AZURE_SQL_DATABASE!,
        options: {
          encrypt: true,
          trustServerCertificate: false
        },
        pool: {
          max: 10,
          min: 0,
          idleTimeoutMillis: 30000
        }
      };
      azureConnection = new sql.ConnectionPool(azureConfig);
    }

    await azureConnection.connect();
    return azureConnection;
  }

  @Get('diagnostics/check-images')
  async checkEventImages(): Promise<EventDiagnosticsResponse> {
    this.logger.log('Checking event images');
    
    const events = await this.eventRepository.find({
      select: ['id', 'name', 'imageUrl'],
    });

    this.logger.debug(`Found ${events.length} total events`);
    
    // Log a few sample events to see their image URLs
    this.logger.debug('Sample events:', events.slice(0, 5));

    const eventsWithImages = events.filter(e => e.imageUrl && e.imageUrl !== '');
    const eventsWithoutImages = events.filter(e => !e.imageUrl || e.imageUrl === '');

    return {
      totalEvents: events.length,
      eventsWithImages: eventsWithImages.length,
      eventsWithoutImages: eventsWithoutImages.length,
      events: eventsWithoutImages.map(e => ({
        id: e.id,
        name: e.name,
        imageUrl: e.imageUrl
      }))
    };
  }

  @Post('sync/fix-event-images')
  async fixEventImages(): Promise<EventImageFixResponse> {
    this.logger.log('Starting event image fix process via service');

    try {
      // Import the fix-event-images function
      const { processEventImagesFromAzure } = require('../../scripts/fix-event-images');

      // Run the image fix using the existing DataSource
      const result = await processEventImagesFromAzure(this.dataSource, false);

      this.logger.log(`Event image fix completed: ${result.success} images processed successfully`);

      return {
        total: result.total || 0,
        processed: result.processed || 0,
        success: result.success || 0,
        skipped: result.skipped || 0,
        errors: result.errors || 0
      };

    } catch (error) {
      this.logger.error('Error during event image fix:', error);
      throw new Error(`Failed to fix event images: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  @Post('migration/reset-events-table')
  async resetEventsTable(): Promise<{ success: boolean; message: string }> {
    this.logger.log('Starting events table reset');
    
    try {
      // Use the DatabaseResetService directly instead of spawning a process
      await this.databaseResetService.resetDatabase(['event']);
      
      this.logger.log('Events table reset completed successfully');
      
      return {
        success: true,
        message: 'Events table reset completed successfully'
      };
    } catch (error) {
      this.logger.error(`Error resetting events table: ${error.message}`);
      throw error;
    }
  }

  @Post('migration/trigger-full-migration')
  async triggerFullMigration(): Promise<MigrationSummary> {
    this.logger.log('Starting full migration with enhanced duplicate detection...');
    
    let azureConnection: sql.ConnectionPool | null = null;
    
    try {
      azureConnection = await this.createAzureConnection();
      this.logger.log('Azure connection established for full migration');
      
      // Enhanced migration configuration with aggressive duplicate detection
      const migrationConfig = {
        skipDuplicates: true,
        enableFuzzyDuplicateDetection: true,
        batchSize: 50, // Smaller batches for better transaction handling
        maxRetries: 3,
        venueDuplicateDetection: {
          nameThreshold: 2,
          addressThreshold: 3,
          proximityThresholdKm: 0.3, // 300 meters
          enableFuzzyMatching: true,
          enableGeographicMatching: true
        }
      };
      
      const orchestrator = new MigrationOrchestrator(this.dataSource, migrationConfig);
      this.logger.log('Full migration orchestrator initialized with enhanced duplicate detection, starting migration...');
      
      const summary = await orchestrator.migrateNewAzureEvents(
         azureConnection,
         true, // fullSync
         undefined, // limit
         false // prioritizeUpcoming
       );
      // Run image fix as part of the migration flow to avoid a second client call
      try {
        this.logger.log('Starting post-migration event image fix');
        const imageFixResult = await this.fixEventImages();
        this.logger.log(`Post-migration event image fix completed: ${JSON.stringify(imageFixResult)}`);
      } catch (err: any) {
        this.logger.error(`Post-migration image fix failed: ${err?.message || err}`);
      }

      this.logger.log('Full migration completed successfully');
      return summary;
    } finally {
      if (azureConnection) {
        await azureConnection.close();
        this.logger.log('Azure connection closed');
      }
    }
  }

  @Post('migration/trigger-limited-migration')
  async triggerLimitedMigration(
    @Body() body: { limit?: number; prioritizeUpcoming?: boolean }
  ): Promise<MigrationSummary> {
    const { limit = 1000, prioritizeUpcoming = true } = body;
    
    this.logger.log(`Starting limited migration with enhanced duplicate detection - limit: ${limit}, prioritizeUpcoming: ${prioritizeUpcoming}`);
    
    let azureConnection: sql.ConnectionPool | null = null;
    
    try {
      azureConnection = await this.createAzureConnection();
      this.logger.log('Azure connection established for limited migration');
      
      // Enhanced migration configuration with aggressive duplicate detection
      const migrationConfig = {
        skipDuplicates: true,
        enableFuzzyDuplicateDetection: true,
        batchSize: 100, // Standard batch size for limited migration
        maxRetries: 3,
        venueDuplicateDetection: {
          nameThreshold: 2,
          addressThreshold: 3,
          proximityThresholdKm: 0.3, // 300 meters
          enableFuzzyMatching: true,
          enableGeographicMatching: true
        }
      };
      
      const orchestrator = new MigrationOrchestrator(this.dataSource, migrationConfig);
      this.logger.log('Limited migration orchestrator initialized with enhanced duplicate detection, starting migration...');
      
      const summary = await orchestrator.migrateNewAzureEvents(
         azureConnection,
         false, // fullSync
         limit, // limit
         prioritizeUpcoming // prioritizeUpcoming
       );
      // Run image fix as part of the migration flow to avoid a second client call
      try {
        this.logger.log('Starting post-limited-migration event image fix');
        const imageFixResult = await this.fixEventImages();
        this.logger.log(`Post-limited-migration event image fix completed: ${JSON.stringify(imageFixResult)}`);
      } catch (err: any) {
        this.logger.error(`Post-limited-migration image fix failed: ${err?.message || err}`);
      }

      this.logger.log('Limited migration completed successfully');
      return summary;
    } catch (error) {
      this.logger.error(`Error during limited migration: ${error.message}`);
      throw error;
    } finally {
      if (azureConnection) {
        await azureConnection.close();
        this.logger.log('Azure connection closed');
      }
    }
  }

  @Get('migration/status')
  async getMigrationStatus(): Promise<{
    lastMigration: Date | null;
    totalEvents: number;
    migrationModulesVersion: string;
  }> {
    this.logger.log('Fetching migration status');
    
    try {
      // Get last migration timestamp
      const lastMigrationResult = await this.dataSource.query(`
        SELECT last_migration_timestamp FROM azure_migration_tracking 
        WHERE migration_type = 'events' 
        ORDER BY last_migration_timestamp DESC 
        LIMIT 1
      `);
      
      const lastMigration = lastMigrationResult.length > 0 
        ? lastMigrationResult[0].last_migration_timestamp 
        : null;
      
      // Get total events count
      const totalEvents = await this.eventRepository.count();
      
      return {
        lastMigration,
        totalEvents,
        migrationModulesVersion: '1.0.0'
      };
    } catch (error) {
      this.logger.error(`Error fetching migration status: ${error.message}`);
      throw error;
    }
  }
}
