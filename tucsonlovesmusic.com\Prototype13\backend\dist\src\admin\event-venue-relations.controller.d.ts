import { DataSource } from 'typeorm';
interface EventMissingVenue {
    id: string;
    name: string;
    date: string;
    azure_venue_id: string;
    azure_venue_name: string;
    postgres_venue_id: string | null;
    postgres_venue_name: string | null;
}
interface EventVenueStatusCounts {
    total: number;
    missingVenues: number;
    potentialFixes: number;
    mismatchedVenues: number;
}
declare class UpdateEventVenueDto {
    eventId: string;
    venueId: string;
}
export declare class EventVenueRelationsController {
    private dataSource;
    private azureConfig;
    private azurePoolPromise;
    private connectionRetryAttempts;
    private connectionRetryDelay;
    constructor(dataSource: DataSource);
    private getAzurePool;
    private closeExistingPool;
    private createNewPool;
    getEventVenueStatusCounts(): Promise<EventVenueStatusCounts>;
    getEventsWithMissingVenues(): Promise<EventMissingVenue[]>;
    getEventsWithMismatchedVenues(): Promise<EventMissingVenue[]>;
    fixEventVenueRelationship(updateDto: UpdateEventVenueDto): Promise<{
        success: boolean;
        message: string;
    }>;
    fixAllEventVenueRelationships(): Promise<{
        success: boolean;
        message: string;
        fixed: number;
        errors: number;
    }>;
    fixEventVenueRelationshipsFromCsv(): Promise<any>;
}
export {};
