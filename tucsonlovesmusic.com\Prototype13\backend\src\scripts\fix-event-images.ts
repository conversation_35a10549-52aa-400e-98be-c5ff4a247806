import * as dotenv from 'dotenv';
import * as path from 'path';
import * as fs from 'fs';
import { DataSource } from 'typeorm';
import axios from 'axios';
import { S3Client, PutObjectCommand, HeadObjectCommand } from '@aws-sdk/client-s3';
import * as mssql from 'mssql';

// Import required entities
import { Event } from '../events/events.entity';

// Load environment variables
dotenv.config();

// Set required environment variables if they don't exist
process.env.AWS_ACCESS_KEY_ID = process.env.AWS_ACCESS_KEY_ID || 'dummy-key';
process.env.AWS_SECRET_ACCESS_KEY = process.env.AWS_SECRET_ACCESS_KEY || 'dummy-secret';
process.env.AWS_REGION = process.env.AWS_REGION || 'us-west-2';
process.env.AWS_S3_BUCKET = process.env.AWS_S3_BUCKET || 'tucsonlovesmusic';

// Azure SQL connection configuration with best practices
const azureConfig = {
  server: process.env.AZURE_DB_SERVER || 'mssql.drv1.umbhost.net',
  database: process.env.AZURE_DB_NAME || 'TLM',
  user: process.env.AZURE_DB_USER || 'Reader',
  password: process.env.AZURE_DB_PASSWORD || 'TLM1234!',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
    connectTimeout: 30000, // 30 second timeout for connections
    requestTimeout: 30000  // 30 second timeout for requests
  },
  pool: {
    max: 10,           // Maximum number of connections
    min: 1,            // Minimum of 1 connection maintained
    idleTimeoutMillis: 30000,  // Close idle connections after 30 seconds
    acquireTimeoutMillis: 15000 // 15 seconds timeout when acquiring connection
  }
};

// Define the result interface
export interface EventImageFixResult {
  total: number;
  processed: number;
  success: number;
  skipped: number;
  errors: number;
  fixedEvents?: Array<{ id: string; name: string; imageUrl: string }>;
}

export async function processEventImagesFromAzure(dataSource: DataSource, dryRun: boolean = false): Promise<EventImageFixResult> {
  console.log('Starting event image processing from Azure...');
  console.log(`Mode: ${dryRun ? 'DRY RUN' : 'EXECUTE'}`);

  // Initialize connection objects
  let azurePool = null;
  
  try {
    console.log('Using provided DataSource for PostgreSQL connection');

    // Connect to Azure SQL
    azurePool = await mssql.connect(azureConfig);
    console.log('Azure SQL connection initialized');
    
    // Create S3 client
    const s3Client = new S3Client({
      region: process.env.AWS_REGION || 'us-west-2',
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || ''
      }
    });
    
    const BUCKET_NAME = process.env.AWS_S3_BUCKET || 'tucsonlovesmusic';
    
    // Stats for tracking progress
    const stats = {
      total: 0,
      processed: 0,
      success: 0,
      errors: 0,
      skipped: 0,
      notFound: 0
    };

    // Track fixed events for the response
    const fixedEvents: Array<{ id: string; name: string; imageUrl: string }> = [];
    
    // Query Azure SQL for events with ProfileImageCropperValue OR ProfileImageId
    const azureResult = await azurePool.request()
      .query(`
        SELECT 
            pe.Id, 
            pe.Name, 
            pe.ProfileImageCropperValue,
            pe.ProfileImageId,
            un.text as MediaName,
            un.uniqueId as MediaGuid,
            pd.textValue as MediaPath
        FROM PerformanceEvents pe
        LEFT JOIN umbracoNode un ON pe.ProfileImageId = un.id 
            AND un.nodeObjectType = 'b796f64c-1f99-4ffb-b886-4bf4bc011a9c'
        LEFT JOIN umbracoContentVersion cv ON un.id = cv.nodeId
        LEFT JOIN umbracoPropertyData pd ON cv.id = pd.versionId 
            AND pd.propertyTypeId IN (
                SELECT id FROM cmsPropertyType 
                WHERE alias = 'umbracoFile'
            )
        -- Removed restrictive WHERE clause to include all events
        -- The script logic will handle events without image sources by skipping them
      `);
    
    const azureEvents = azureResult.recordset;
    stats.total = azureEvents.length;
    console.log(`Found ${azureEvents.length} events with image data in Azure SQL`);
    
    // Base URL for fetching source images
    const BASE_URL = process.env.MEDIA_SOURCE_URL || 'https://tucsonlovesmusic.com';
    
    // Process each event from Azure
    for (const azureEvent of azureEvents) {
      try {
        let sourceUrl = null;
        let imageSource = 'none';
        
        // Try ProfileImageCropperValue first (existing logic)
        if (azureEvent.ProfileImageCropperValue) {
          try {
            const imageData = JSON.parse(azureEvent.ProfileImageCropperValue);
            if (imageData && imageData.Src) {
              sourceUrl = imageData.Src.startsWith('http') ? 
                imageData.Src : `${BASE_URL}${imageData.Src}`;
              imageSource = 'cropper';
            }
          } catch (error) {
            console.warn(`Could not parse ProfileImageCropperValue for event ${azureEvent.Name} (${azureEvent.Id}): ${error instanceof Error ? error.message : 'Unknown error'}`);
          }
        }
        
        // Fallback to Umbraco media node if no cropper value
        if (!sourceUrl && azureEvent.MediaPath) {
          try {
            // Try to parse as JSON first (Umbraco format)
            const mediaData = JSON.parse(azureEvent.MediaPath);
            if (mediaData && mediaData.Src) {
              sourceUrl = mediaData.Src.startsWith('http') ? 
                mediaData.Src : `${BASE_URL}${mediaData.Src}`;
              imageSource = 'umbraco';
            }
          } catch (jsonError) {
            // Fallback to original logic for non-JSON data
            if (azureEvent.MediaPath.startsWith('http')) {
              sourceUrl = azureEvent.MediaPath;
              imageSource = 'direct';
            } else if (azureEvent.MediaPath.includes('/media/')) {
              // Extract media path
              const mediaMatch = azureEvent.MediaPath.match(/\/media\/[^\s"'}]+/);
              if (mediaMatch) {
                sourceUrl = `${BASE_URL}${mediaMatch[0]}`;
                imageSource = 'direct';
              }
            } else {
              sourceUrl = azureEvent.MediaPath.startsWith('http') ? 
                azureEvent.MediaPath : `${BASE_URL}${azureEvent.MediaPath}`;
              imageSource = 'direct';
            }
          }
        }
        
        // If no source URL found via ProfileImageId, try searching by event name
        if (!sourceUrl) {
          console.log(`⚠️  No ProfileImageId source for event: ${azureEvent.Name} (${azureEvent.Id}), searching by name...`);
          
          // Search for media by event name in Umbraco
          const nameSearchResult = await azurePool.request().query(`
            SELECT TOP 1
              un.id,
              un.text,
              un.uniqueId,
              pd.textValue
            FROM umbracoNode un
            INNER JOIN umbracoContentVersion cv ON un.id = cv.nodeId AND cv.[current] = 1
            INNER JOIN umbracoPropertyData pd ON cv.id = pd.versionId
            INNER JOIN cmsPropertyType pt ON pd.propertyTypeId = pt.id
            WHERE (un.text LIKE '%${azureEvent.Name.replace(/'/g, "''")}%' 
                   OR pd.textValue LIKE '%${azureEvent.Name.replace(/'/g, "''")}%')
              AND pt.alias = 'umbracoFile'
              AND pd.textValue IS NOT NULL
            ORDER BY un.id DESC
          `);
          
          if (nameSearchResult.recordset.length > 0) {
            const mediaNode = nameSearchResult.recordset[0];
            console.log(`✓ Found media by name: ${mediaNode.text} (ID: ${mediaNode.id})`);
            
            try {
              // Try to parse as JSON first (Umbraco format)
              const mediaData = JSON.parse(mediaNode.textValue);
              if (mediaData && mediaData.Src) {
                sourceUrl = mediaData.Src.startsWith('http') ? 
                  mediaData.Src : `${BASE_URL}${mediaData.Src}`;
                imageSource = 'name-search';
              }
            } catch (jsonError) {
              // Fallback to direct path
              if (mediaNode.textValue.includes('/media/')) {
                const mediaMatch = mediaNode.textValue.match(/\/media\/[^\s"'}]+/);
                if (mediaMatch) {
                  sourceUrl = `${BASE_URL}${mediaMatch[0]}`;
                  imageSource = 'name-search-direct';
                }
              }
            }
          }
        }
        
        // Check if we have a source path after all attempts
        if (!sourceUrl) {
          console.log(`❌ No image source found for event: ${azureEvent.Name} (${azureEvent.Id})`);
          stats.skipped++;
          continue;
        }
        
        // Find the corresponding event in PostgreSQL using azure_id
        const eventRepository = dataSource.getRepository(Event);
        const pgEvent = await eventRepository.findOne({
          where: { azure_id: azureEvent.Id },
          select: ['id', 'name', 'imageUrl']
        });
        
        if (!pgEvent) {
          console.warn(`No matching PostgreSQL event found for Azure ID: ${azureEvent.Id}`);
          stats.notFound++;
          continue;
        }
        
        // Skip if the event already has an image URL
        if (pgEvent.imageUrl && pgEvent.imageUrl.trim() !== '') {
          console.log(`Event ${pgEvent.name} (${pgEvent.id}) already has an image URL: ${pgEvent.imageUrl}`);
          stats.skipped++;
          continue;
        }
        
        console.log(`\n📸 Processing: ${azureEvent.Name} (${azureEvent.Id})`);
        console.log(`Source: ${sourceUrl}`);
        console.log(`Image source method: ${imageSource}`);
        
        if (imageSource.includes('name-search')) {
          console.log(`🔍 Found via name search fallback (ProfileImageId was NULL)`);
        }
        
        // Generate S3 key using uppercase Azure ID (consistent with existing format)
        const upperEventId = azureEvent.Id.toUpperCase();
        const s3Key = `events/${upperEventId}.jpg`;
        
        // Check if image already exists in S3
        let exists = false;
        try {
          await s3Client.send(new HeadObjectCommand({
            Bucket: BUCKET_NAME,
            Key: s3Key
          }));
          exists = true;
        } catch (error) {
          exists = false;
        }
        
        if (exists) {
          console.log(`Image already exists in S3 for event ${pgEvent.name} (${pgEvent.id})`);
          
          // Even if the image exists, update the PostgreSQL record if it doesn't have the imageUrl
          const s3Url = `https://${BUCKET_NAME}.s3.${process.env.AWS_REGION || 'us-west-2'}.amazonaws.com/${s3Key}`;

          if (!dryRun) {
            await eventRepository.update(pgEvent.id, { imageUrl: s3Url });
          }

          console.log(`${dryRun ? '[DRY RUN] Would update' : 'Updated'} event ${pgEvent.name} (${pgEvent.id}) with existing S3 URL: ${s3Url}`);
          stats.success++;

          // Track the fixed event
          fixedEvents.push({
            id: pgEvent.id,
            name: pgEvent.name || 'Unknown Event',
            imageUrl: s3Url
          });
        } else {
          // Fetch image from source
          console.log(`Fetching image from ${sourceUrl}`);
          
          try {
            const response = await axios.get(sourceUrl, { 
              responseType: 'arraybuffer',
              headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
              }
            });
            
            if (response.status === 200) {
              // Upload to S3
              console.log(`Uploading image for event ${pgEvent.name} (${pgEvent.id}) to S3...`);
              
              try {
                await s3Client.send(new PutObjectCommand({
                  Bucket: BUCKET_NAME,
                  Key: s3Key,
                  Body: Buffer.from(response.data),
                  ContentType: response.headers['content-type'] || 'image/jpeg',
                  ACL: 'public-read'
                }));
                
                // Generate S3 URL
                const s3Url = `https://${BUCKET_NAME}.s3.${process.env.AWS_REGION || 'us-west-2'}.amazonaws.com/${s3Key}`;

                // Update event with S3 URL using TypeORM
                if (!dryRun) {
                  await eventRepository.update(pgEvent.id, { imageUrl: s3Url });
                }

                console.log(`${dryRun ? '[DRY RUN] Would update' : 'Successfully updated'} event ${pgEvent.name} (${pgEvent.id}) with S3 URL: ${s3Url}`);
                stats.success++;

                // Track the fixed event
                fixedEvents.push({
                  id: pgEvent.id,
                  name: pgEvent.name || 'Unknown Event',
                  imageUrl: s3Url
                });
              } catch (uploadError) {
                console.error(`Error uploading to S3: ${uploadError instanceof Error ? uploadError.message : 'Unknown error'}`);
                stats.errors++;
              }
            } else {
              throw new Error(`Failed to fetch image: HTTP ${response.status}`);
            }
          } catch (error) {
            console.error(`Error fetching/uploading image for event ${pgEvent.name} (${pgEvent.id}): ${error instanceof Error ? error.message : 'Unknown error'}`);
            stats.errors++;
            continue;
          }
        }
      } catch (error) {
        console.error(`Error processing event ${azureEvent.Name} (${azureEvent.Id}): ${error instanceof Error ? error.message : 'Unknown error'}`);
        stats.errors++;
      } finally {
        stats.processed++;
        
        // Log progress every 10 events
        if (stats.processed % 10 === 0 || stats.processed === stats.total) {
          console.log(`Progress: ${stats.processed}/${stats.total} (${Math.round((stats.processed / stats.total) * 100)}%)`);
        }
      }
    }
    
    // Print summary
    console.log('\n=== IMAGE PROCESSING SUMMARY ===');
    console.log(`Total events found in Azure: ${stats.total}`);
    console.log(`Events processed: ${stats.processed}`);
    console.log(`Successfully updated: ${stats.success}`);
    console.log(`Skipped (has image or no valid source): ${stats.skipped}`);
    console.log(`Not found in PostgreSQL: ${stats.notFound}`);
    console.log(`Errors encountered: ${stats.errors}`);

    // Return the results in the expected format
    return {
      total: stats.total,
      processed: stats.processed,
      success: stats.success,
      skipped: stats.skipped,
      errors: stats.errors,
      fixedEvents: fixedEvents
    };
  } catch (error) {
    console.error(`Error in process: ${error instanceof Error ? error.message : 'Unknown error'}`);
    if (error instanceof Error && error.stack) {
      console.error(`Error stack: ${error.stack}`);
    }
    throw error;
  } finally {
    // Close only the Azure SQL connection (DataSource is managed externally)
    if (azurePool) {
      await azurePool.close();
      console.log('Azure SQL connection closed');
    }
  }
}

// Bootstrap function for standalone script execution
async function bootstrap() {
  console.log('Starting event image fix script (Azure to S3)...');
  console.log('Environment variables loaded from:', path.resolve(__dirname, '../../.env'));

  // This is only used when running as a standalone script
  // When called from the API, the DataSource is provided
  const { createConnection } = require('typeorm');

  try {
    // Create connection for standalone execution
    const dbUrl = process.env.DATABASE_URL;
    if (!dbUrl) {
      throw new Error('DATABASE_URL environment variable is not set');
    }

    const dataSource = await createConnection({
      type: 'postgres',
      url: dbUrl,
      entities: [Event],
      synchronize: false,
      ssl: { rejectUnauthorized: false },
      extra: {
        max: 10,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 10000
      },
      logging: false
    });

    // Run the image processing function
    const result = await processEventImagesFromAzure(dataSource, false);
    console.log('Script completed successfully');

    // Close the connection
    await dataSource.close();
    console.log('PostgreSQL connection closed');

  } catch (error) {
    console.error('Error running script:', error);
  }
}

// Only run bootstrap if this file is executed directly (not imported)
if (require.main === module) {
  bootstrap();
}