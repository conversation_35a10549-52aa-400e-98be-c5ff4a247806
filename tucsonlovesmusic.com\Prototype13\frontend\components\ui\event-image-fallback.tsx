"use client";

import { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { Calendar as CalendarIcon } from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Event } from '@/types/events';
import { config } from '@/lib/config';
import { ImagePlaceholder } from './image-placeholder';

// Global cache for image URLs to prevent unnecessary reloading
const imageCache = new Map<string, string>();
const imageLoadingCache = new Map<string, boolean>();

interface EventImageFallbackProps {
  event: Event;
  fillContainer?: boolean;
  priority?: boolean;
  size?: number;
  className?: string;
  skipValidation?: boolean;
  objectFit?: 'cover' | 'contain';
}

export function EventImageFallback({
  event,
  fillContainer = false,
  className,
  skipValidation = false,
  priority = false,
  objectFit = 'cover',
}: EventImageFallbackProps) {
  const [imageUrl, setImageUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [hasError, setHasError] = useState<boolean>(false);
  const isMountedRef = useRef<boolean>(true);
  const triedUrlsRef = useRef<Set<string>>(new Set());
  
  // Create a unique key for this event for caching
  const eventCacheKey = `${event.id}-image`;
  
  // Log the entire event object for debugging
  useEffect(() => {
    // Removed development log for Full Event Object
  }, [event]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Add specific logging for problematic events
  useEffect(() => {
    if (
      event.name.includes('Short Notice') || 
      event.name.includes('Open Jam Night') ||
      event.name.includes('Open Mic')
    ) {
      // Removed development log for PROBLEMATIC EVENT DETAILS
    }
  }, [event]);

  // Function to handle image loading errors
  const handleImageError = useCallback(() => {
    if (isMountedRef.current) {
      setHasError(true);
      setIsLoading(false);
      imageLoadingCache.set(eventCacheKey, false);
    }
  }, [eventCacheKey]);

  // Format S3 URLs correctly
  const formatS3Url = useCallback((url: string): string => {
    if (!url) return '';
    
    // Remove any "undefined/" prefix that might be present
    let cleanUrl = url.replace(/^undefined\//, '');
    
    // If it's already a full URL, return it as is
    if (cleanUrl.startsWith('http')) {
      
      return cleanUrl;
    }
    
    // Special case for venue images that might have a specific format
    if (cleanUrl.includes('venues/') || cleanUrl.includes('venue/')) {
      // Ensure we have a clean path without leading slashes
      cleanUrl = cleanUrl.replace(/^\/+/, '');
      const fullUrl = `https://tucsonlovesmusic.s3.us-west-2.amazonaws.com/${cleanUrl}`;
      
      return fullUrl;
    }
    
    // Otherwise, assume it's a relative path and add the S3 bucket URL
    cleanUrl = cleanUrl.replace(/^\/+/, '');
    const fullUrl = `https://tucsonlovesmusic.s3.us-west-2.amazonaws.com/${cleanUrl}`;
    
    return fullUrl;
  }, []);

  // Find and set the image URL following proper priority hierarchy
  useEffect(() => {
    // Skip if no event ID
    if (!event?.id) return;
    
    // Reset state
    if (isMountedRef.current) {
      setImageUrl('');
      setIsLoading(true);
      setHasError(false);
    }

    // Debug logging for Terry Fox specifically
    if (event.name && event.name.includes('Terry Fox')) {
      console.log('[EventImageFallback] Terry Fox event data:', {
        eventImageUrl: event.imageUrl,
        talents: event.talents,
        venue: event.venue
      });
      console.log('[EventImageFallback] Terry Fox - This event has an empty talents array, which suggests a data linking issue between the event and Terry Fox as a performer.');
    }
    
    // Helper function to validate and format URLs
    const validateAndFormatUrl = (url: string | undefined): string | null => {
      if (!url || url.trim() === '' || url === 'null' || url === 'undefined') {
        return null;
      }
      
      // Avoid processing placeholder URLs that should not be turned into S3 URLs
      if (url.includes('venue-placeholder') || url.includes('placeholder') || url.includes('/images/venue-placeholder.jpg')) {
        return null;
      }
      
      return formatS3Url(url);
    };
    
    // Priority 1: Event image (direct imageUrl)
    if (event.imageUrl) {
      const formattedUrl = validateAndFormatUrl(event.imageUrl);
      if (formattedUrl && isMountedRef.current) {
        console.log(`[EventImageFallback] Using event image for ${event.name}:`, formattedUrl);
        setImageUrl(formattedUrl);
        setIsLoading(false);
        imageCache.set(eventCacheKey, formattedUrl);
        imageLoadingCache.set(eventCacheKey, false);
        return;
      }
    }
    
    // Priority 2: Event media images
    if (Array.isArray(event.media) && event.media.length > 0) {
      const mediaImage = event.media.find(m => m.type === 'image' || !m.type);
      if (mediaImage?.url) {
        const formattedUrl = validateAndFormatUrl(mediaImage.url);
        if (formattedUrl && isMountedRef.current) {
          setImageUrl(formattedUrl);
          setIsLoading(false);
          imageCache.set(eventCacheKey, formattedUrl);
          imageLoadingCache.set(eventCacheKey, false);
          return;
        }
      }
    }
    
    // Priority 3: Talent images (first talent with an image)
    if (event.talents && event.talents.length > 0) {
      for (const talent of event.talents) {
        if (talent.imageUrl) {
          const formattedUrl = validateAndFormatUrl(talent.imageUrl);
          if (formattedUrl && isMountedRef.current) {
            console.log(`[EventImageFallback] Using talent image for ${event.name} (talent: ${talent.name}):`, formattedUrl);
            setImageUrl(formattedUrl);
            setIsLoading(false);
            imageCache.set(eventCacheKey, formattedUrl);
            imageLoadingCache.set(eventCacheKey, false);
            return;
          }
        }
      }
    }
    
    // Priority 4: Venue images
    if (event.venue) {
      // Try imageUrl first, then image_url
      const venueImageUrl = event.venue.imageUrl || event.venue.image_url;
      if (venueImageUrl) {
        console.log(`[EventImageFallback] Checking venue image for ${event.name} (venue: ${event.venue.name}):`, venueImageUrl);
        
        // Use validateAndFormatUrl for all venue images to filter out placeholders
        const formattedUrl = validateAndFormatUrl(venueImageUrl);
        if (formattedUrl && isMountedRef.current) {
          console.log(`[EventImageFallback] Using venue image for ${event.name}:`, formattedUrl);
          setImageUrl(formattedUrl);
          setIsLoading(false);
          imageCache.set(eventCacheKey, formattedUrl);
          imageLoadingCache.set(eventCacheKey, false);
          return;
        } else {
          console.log(`[EventImageFallback] Venue image filtered out for ${event.name}:`, venueImageUrl);
        }
      }
    }
    
    // Priority 5: No image found - show placeholder
    if (isMountedRef.current) {
      setHasError(true);
      setIsLoading(false);
    }
  }, [event, eventCacheKey, formatS3Url]);

  return (
    <div className={cn('relative w-full h-full overflow-hidden', className)}>
      {imageUrl ? (
        <Image
          src={imageUrl}
          alt={event.name || 'Event image'}
          className={cn(
            objectFit === 'contain' ? 'object-contain' : 'object-cover',
            'w-full h-full transition-opacity duration-300',
            isLoading ? 'opacity-0' : 'opacity-100'
          )}
          fill
          sizes="(max-width: 768px) 100vw, 50vw"
          priority={priority}
          unoptimized={true} // Skip Next.js image optimization
          onLoad={() => {
            setIsLoading(false);
            imageLoadingCache.set(eventCacheKey, false);
          }}
          onError={() => {
            handleImageError();
          }}
        />
      ) : (
        <div className="flex items-center justify-center w-full h-full bg-muted text-muted-foreground">
          {hasError && (
            <div className="flex flex-col items-center justify-center p-4 text-center">
              <CalendarIcon className="w-8 h-8 mb-2" />
              <span className="text-xs">No image available</span>
            </div>
          )}
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-background/80">
              <LoadingSpinner size="md" />
            </div>
          )}
        </div>
      )}
    </div>
  );
}
