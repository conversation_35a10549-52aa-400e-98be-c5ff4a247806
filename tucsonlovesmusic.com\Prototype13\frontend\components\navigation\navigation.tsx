"use client";

import { useState, useEffect, useMemo, useRef } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, User, ShieldCheck, Heart, Building2, Music, Home, Calendar, Star } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON>et<PERSON>ontent,
  Sheet<PERSON>rigger,
  SheetTitle,
  SheetDescription,
} from "@/components/ui/sheet";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuGroup,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useAuth0 } from "@auth0/auth0-react";
import { UnifiedSearch } from "@/components/search/unified-search";
import { BasicSearch } from "@/components/search/basic-search";
import { useAdminRole } from "@/hooks/use-admin-role";
import { useVenueRole } from "@/hooks/use-venue-role";
import { useMusicianRole } from "@/hooks/use-musician-role";
import { config } from "@/lib/config";
import { ThemeToggle } from "@/components/theme/theme-toggle";
import { VisuallyHidden } from "@/components/ui/visually-hidden";
import Image from 'next/image';

// Helper function to get default nickname from email
function getDefaultNickname(email: string | undefined): string {
  if (!email) return "";
  return email.split('@')[0].replace(/[^a-zA-Z0-9]/g, '');
}

const routes = [
  {
    href: "/",
    label: "Home",
    icon: <Home className="h-4 w-4 mr-2" />
  },
  {
    href: "/events",
    label: "Events",
    icon: <Calendar className="h-4 w-4 mr-2" />
  },
  {
    href: "/musicians",
    label: "Musicians",
    icon: <Music className="h-4 w-4 mr-2" />
  },
  {
    href: "/venues",
    label: "Venues",
    icon: <Building2 className="h-4 w-4 mr-2" />
  },
  {
    href: "/membership",
    label: "Membership",
    icon: <Star className="h-4 w-4 mr-2" />
  },
];

export function Navigation() {
  const pathname = usePathname();
  const router = useRouter();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isRouterReady, setIsRouterReady] = useState(false);
  const { loginWithRedirect, logout, isAuthenticated, user, isLoading: authLoading } = useAuth0();
  const { isAdmin, isLoading: adminLoading } = useAdminRole();
  const { isVenueOwner, isLoading: venueLoading } = useVenueRole();
  const { isMusician, isLoading: musicianLoading } = useMusicianRole();
  const [dashboardDropdownOpen, setDashboardDropdownOpen] = useState(false);
  const dashboardRef = useRef<HTMLDivElement>(null);
  const isLoading = authLoading || adminLoading || venueLoading || musicianLoading;

  // Function to handle navigation while closing the dropdown
  const handleNavigation = (path: string) => {
    // Close all dropdowns
    setIsMenuOpen(false);
    setDashboardDropdownOpen(false);
    
    // Use a hybrid approach - first close dropdowns, then navigate using window.location
    // This ensures the dropdown is fully closed before navigation
    setTimeout(() => {
      window.location.href = path;
    }, 50);
  };

  const dashboardTabs = useMemo(() => {
    if (isLoading) return [];
    
    const tabs = [];
    
    if (isAdmin) {
      tabs.push({
        href: "/dashboard?tab=admin",
        label: "Admin",
        icon: <ShieldCheck className="h-4 w-4 mr-2" />
      });
    }
    
    tabs.push({
      href: "/dashboard?tab=favorites",
      label: "Favorites",
      icon: <Heart className="h-4 w-4 mr-2" />
    });
    
    tabs.push({
      href: "/dashboard?tab=account",
      label: "Account",
      icon: <User className="h-4 w-4 mr-2" />
    });
    
    return tabs;
  }, [isLoading, isAdmin, isVenueOwner, isMusician]);

  // Set router ready state once
  useEffect(() => {
    if (!isRouterReady) {
      setIsRouterReady(true);
    }
  }, [isRouterReady]);

  // Handle clicks outside the dropdown to close it
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (dashboardDropdownOpen && 
          dashboardRef.current && 
          !dashboardRef.current.contains(e.target as Node)) {
        setDashboardDropdownOpen(false);
      }
    };

    if (dashboardDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [dashboardDropdownOpen]);

  const handleLogin = async () => {
    try {
      console.log('Login configuration:', {
        audience: config.auth0.audience,
        scope: config.auth0.scope
      });

      await loginWithRedirect({
        appState: { 
          returnTo: pathname ?? '/'
        },
        authorizationParams: {
          audience: config.auth0.audience,
          scope: config.auth0.scope,
          prompt: 'login'
        }
      });
    } catch (error) {
      console.error("Login error:", error);
    }
  };
  
  const handleLogout = async () => {
    if (!isRouterReady) return;
    
    const hostname = window.location.hostname;
    const returnTo = `https://${hostname}`;
    
    console.log('Logout configuration:', { 
      hostname,
      returnTo,
      currentUrl: window.location.href
    });
    
    await logout({ 
      logoutParams: {
        returnTo,
        clientId: config.auth0.clientId
      }
    });
  };

  // Compute the full routes array including admin route when appropriate
  const fullRoutes = useMemo(() => {
    // Always return base routes, admin functionality will be accessed through user menu
    return routes;
  }, []);

  // Don't render anything during initial loading to prevent flashing
  if (!isRouterReady) {
    return null;
  }

  const displayName = user?.nickname || user?.name || getDefaultNickname(user?.email);

  return (
    <header className="sticky top-0 z-[1000] w-full border-b bg-background backdrop-blur supports-[backdrop-filter]:bg-background" style={{ pointerEvents: 'auto' }}>
      <div className="flex h-16 lg:h-[4.5rem] items-center justify-between px-4 sm:container">
        {/* Left: Logo (mobile/tablet only) */}
        <div className="flex items-center">
          <div className="lg:hidden">
            <Link href="/" className="block px-3 py-2 rounded-lg bg-card/95 dark:bg-background/80 border border-border/50 shadow-sm hover:shadow-md hover:scale-[1.02] transition-all duration-200">
              <Image 
                src="/tlm-logo-singlerow.svg"
                alt="TUCSON LOVES MUSIC"
                width={120} 
                height={27}
                className="rounded-md" 
              />
            </Link>
          </div>
          {/* Desktop logo */}
          <div className="hidden lg:flex">
            <Link href="/" className="mr-6 flex items-center space-x-2 px-3 py-2 rounded-lg bg-card/95 dark:bg-background/80 border border-border/50 shadow-sm hover:shadow-md hover:scale-[1.02] transition-all duration-200">
              <Image 
                src="/tlm-logo-singlerow.svg"
                alt="TUCSON LOVES MUSIC"
                width={150} 
                height={33}
                className="hidden sm:block" 
              />
            </Link>
          </div>
        </div>
        {/* Desktop horizontal nav links */}
        <nav className="hidden lg:flex items-center gap-6 text-sm ml-4">
          {fullRoutes.map((route) => (
            <Link
              key={route.href}
              href={route.href}
              className={cn(
                "transition-all duration-200 hover:text-primary font-medium px-4 py-2.5 rounded-lg relative border-b-2 hover:scale-[1.02] hover:shadow-sm",
                pathname ? (
                  route.href !== "/" && pathname.startsWith(route.href)
                    ? "text-primary bg-primary/10 border-primary hover:bg-primary/15 shadow-sm"
                    : pathname === route.href
                      ? "text-primary bg-primary/10 border-primary hover:bg-primary/15 shadow-sm"
                      : "text-muted-foreground border-transparent hover:bg-accent/60 hover:border-muted-foreground/20"
                ) : "text-muted-foreground border-transparent hover:bg-accent/60 hover:border-muted-foreground/20"
              )}
              style={{ pointerEvents: 'auto' }}
            >
              {route.label}
            </Link>
          ))}
        </nav>
        {/* Right: Hamburger menu (mobile) or avatar dropdown (desktop) */}
        <div className="flex items-center gap-4 ml-auto">
          {/* Hamburger menu trigger: mobile only */}
          <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
            <SheetTrigger asChild className="lg:hidden">
              <Button variant="ghost" size="icon">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="z-[1001] w-[300px] sm:w-[400px] flex flex-col h-[90vh] max-h-[90vh] overflow-hidden">
              <SheetTitle>
                <VisuallyHidden>Navigation & Account Menu</VisuallyHidden>
              </SheetTitle>
              <SheetDescription>
                <VisuallyHidden>Main navigation and account actions</VisuallyHidden>
              </SheetDescription>
              {/* Profile/account section */}
              <div className="flex flex-col items-start gap-2 py-4 border-b border-border/40 mb-4 flex-shrink-0">
                {isAuthenticated && user ? (
                  <>
                    <div className="flex items-center gap-3 px-2">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={user.picture} alt={displayName} />
                        <AvatarFallback>{displayName.charAt(0).toUpperCase()}</AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col">
                        <span className="font-semibold text-base">{displayName}</span>
                        <span className="text-xs text-muted-foreground">{user.email}</span>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      className="w-full justify-start mt-2"
                      onClick={() => { setIsMenuOpen(false); handleNavigation('/dashboard'); }}
                    >
                      Dashboard
                    </Button>
                  </>
                ) : (
                  <Button
                    variant="default"
                    className="w-full mt-2"
                    onClick={() => { setIsMenuOpen(false); handleLogin(); }}
                  >
                    Sign In
                  </Button>
                )}
              </div>
              {/* Navigation links */}
              <nav className="flex flex-col gap-2 flex-grow">
                {fullRoutes.map((route) => (
                  <Link
                    key={route.href}
                    href={route.href}
                    onClick={() => setIsMenuOpen(false)}
                    className={cn(
                      "text-base font-medium transition-all hover:text-primary px-3 py-1.5 flex items-center rounded-md border-l-4",
                      pathname ? (
                        route.href !== "/" && pathname.startsWith(route.href)
                          ? "text-primary bg-primary/10 border-primary hover:bg-primary/15"
                          : pathname === route.href
                            ? "text-primary bg-primary/10 border-primary hover:bg-primary/15"
                            : "text-muted-foreground border-transparent hover:bg-accent/50 hover:border-muted-foreground/30"
                      ) : "text-muted-foreground border-transparent hover:bg-accent/50 hover:border-muted-foreground/30"
                    )}
                    style={{ pointerEvents: 'auto', position: 'relative' }}
                  >
                    {route.icon}
                    {route.label}
                  </Link>
                ))}
              </nav>
              {/* Account actions (if authenticated) */}
              {isAuthenticated && user && (
                <div className="mt-2 border-t border-border/40 pt-3 flex flex-col gap-1.5 flex-shrink-0">
                  <ThemeToggle label="" />
                  <Button
                    variant="ghost"
                    className="w-full justify-start text-red-500 hover:text-red-600"
                    onClick={() => { setIsMenuOpen(false); handleLogout(); }}
                  >
                    Sign out
                  </Button>
                </div>
              )}
              {/* Logo at the bottom */}
              <div className="mt-auto pb-3 pt-3 border-t border-border/40 flex justify-center flex-shrink-0">
                <Link href="/" onClick={() => setIsMenuOpen(false)} className="px-4 py-3 rounded-lg bg-card/95 dark:bg-background/80 border border-border/50 shadow-sm hover:shadow-md hover:scale-[1.02] transition-all duration-200">
                  <Image 
                    src="/tlm-logo-singlerow.svg"
                    alt="TUCSON LOVES MUSIC"
                    width={180} 
                    height={40}
                    className="opacity-80 hover:opacity-100 transition-opacity" 
                  />
                </Link>
              </div>
            </SheetContent>
          </Sheet>
          {/* Avatar dropdown: desktop only */}
          {isLoading ? (
            <Button variant="ghost" size="sm" disabled className="hidden lg:inline-flex">
              Loading...
            </Button>
          ) : isAuthenticated && user ? (
            <div className="hidden lg:block">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="ghost" 
                    className="relative h-9 w-9 rounded-full transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                  >
                    <Avatar className="h-9 w-9">
                      <AvatarImage src={user.picture} alt={displayName} />
                      <AvatarFallback>
                        {displayName.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <div className="flex items-center justify-start gap-2 p-3">
                    <div className="flex flex-col space-y-1 leading-none">
                      {user?.nickname && (
                        <p className="text-lg font-semibold">{user.nickname}</p>
                      )}
                      {user?.email && (
                        <p className="w-[200px] truncate text-sm text-muted-foreground">
                          {user.email}
                        </p>
                      )}
                    </div>
                  </div>
                  <DropdownMenuSeparator />
                  
                  <div 
                    className="relative cursor-pointer"
                    ref={dashboardRef}
                  >
                    <DropdownMenuItem asChild className="px-3 py-2 flex items-center justify-between group">
                      <div className="flex-1 flex items-center justify-between">
                        <a 
                          href="/dashboard" 
                          className="flex-1"
                          onClick={(e) => {
                            e.preventDefault();
                            handleNavigation("/dashboard");
                          }}
                        >
                          Dashboard
                        </a>
                        {dashboardTabs.length > 0 && (
                          <span 
                            className="ml-2 text-muted-foreground group-hover:text-foreground cursor-pointer"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              setDashboardDropdownOpen(!dashboardDropdownOpen);
                            }}
                            aria-label={dashboardDropdownOpen ? "Collapse dashboard menu" : "Expand dashboard menu"}
                          >
                            {dashboardDropdownOpen ? (
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-chevron-down transition-transform duration-200">
                                <path d="m6 9 6 6 6-6"/>
                              </svg>
                            ) : (
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-chevron-right transition-transform duration-200">
                                <path d="m9 18 6-6-6-6"/>
                              </svg>
                            )}
                          </span>
                        )}
                      </div>
                    </DropdownMenuItem>
                  
                    <div 
                      className={cn(
                        "pl-6 py-1 border-l-2 border-muted ml-5 my-1 transition-all duration-200 ease-in-out",
                        dashboardDropdownOpen ? "opacity-100 max-h-[500px]" : "opacity-0 max-h-0 overflow-hidden pointer-events-none"
                      )}
                    >
                      {dashboardTabs.map((tab) => (
                        <DropdownMenuItem key={tab.href} asChild className="px-3 py-1.5">
                          <a 
                            href={tab.href} 
                            className="flex items-center"
                            onClick={(e) => {
                              e.preventDefault();
                              handleNavigation(tab.href);
                            }}
                          >
                            {tab.icon}
                            <span className="ml-2">{tab.label}</span>
                          </a>
                        </DropdownMenuItem>
                      ))}
                    </div>
                  </div>
                  
                  <DropdownMenuSeparator />
                  <div className="px-3 py-2">
                    <ThemeToggle label="" />
                  </div>
                  
                  <DropdownMenuSeparator />
                  
                  <DropdownMenuItem
                    className="cursor-pointer px-3 py-2 text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-950"
                    onSelect={handleLogout}
                  >
                    Sign out
                  </DropdownMenuItem>

                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          ) : (
            <Button 
              variant="default" 
              size="sm"
              onClick={handleLogin}
              className="hidden lg:inline-flex"
            >
              Sign In
            </Button>
          )}
        </div>
      </div>
    </header>
  );
}