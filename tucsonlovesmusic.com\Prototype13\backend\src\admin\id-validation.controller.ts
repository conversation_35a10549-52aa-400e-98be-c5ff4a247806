import { Controller, Get, Post, Delete, Body, UseGuards, Logger, Query, Param, NotFoundException, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { AuthGuard } from '../auth/auth.guard';
import { ColumnDetector } from '../utils/column-detector';

interface ValidationCounts {
  venueCount: number;
  musicianCount: number;
  eventCount: number;
}

interface ValidationIssue {
  id: string;
  entityType: 'venues' | 'musicians' | 'events';
  postgresRecord?: any;
  azureRecord?: any;
  issueType: 'missing_azure' | 'missing_postgres' | 'id_mismatch' | 'data_mismatch';
  description: string;
  createdAt: string;
}

interface FixIssueDto {
  issueId: string;
  resolution: 'fix' | 'merge';
  matchId?: string; // For selecting from possible matches
  azureId?: string; // For manually linking to existing Azure ID
  mergeFields?: boolean; // Whether to merge fields between PostgreSQL and Azure
  issueType?: 'missing_azure' | 'missing_postgres' | 'id_mismatch' | 'data_mismatch';
  entityType?: 'venues' | 'musicians' | 'events';
  azureRecord?: any; // The Azure record data for creating in PostgreSQL
}

@Controller('admin/id-validation')
@UseGuards(AuthGuard)
export class IdValidationController {
  private readonly logger = new Logger(IdValidationController.name);
  private readonly columnDetector: ColumnDetector;
  
  constructor(@InjectDataSource() private dataSource: DataSource) {
    this.columnDetector = new ColumnDetector(dataSource);
  }
  
  /**
   * Helper method to combine date and time fields from Azure SQL using string values
   * to avoid timezone conversion issues
   */
  private combineDateTime(dateStr: string | null, timeStr: string | null | undefined): string | null {
    if (!dateStr) return null;
    
    // Clean the date string (should be in YYYY-MM-DD format from SQL CONVERT)
    const cleanDateStr = dateStr.includes(' ') ? dateStr.split(' ')[0] : dateStr;
    
    // Use the time string directly or default to 00:00:00
    const cleanTimeStr = timeStr || '00:00:00';
    
    // Combine date and time strings
    return `${cleanDateStr} ${cleanTimeStr}`;
  }

  private async checkAzureDbConnection(): Promise<any> {
    const sql = require('mssql');
    
    try {
      // Azure connection config
      const config = {
        server: 'mssql.drv1.umbhost.net',
        database: 'TLM',
        user: 'Reader',
        password: 'TLM1234!',
        options: {
          encrypt: false,
          trustServerCertificate: true
        }
      };
      
      // Create connection pool
      const pool = await sql.connect(config);
      this.logger.log('Successfully connected to Azure SQL');
      
      return pool;
    } catch (error) {
      this.logger.error(`Failed to connect to Azure SQL: ${error.message}`);
      throw error;
    }
  }

  @Get('issues/events')
  async getEventIssues() {
    try {
      const issues = await this.getMissingEvents();
      this.logger.log(`Returning ${issues.length} event validation issues`);
      return issues;
    } catch (error) {
      this.logger.error(`Error fetching event validation issues: ${error.message}`);
      throw new InternalServerErrorException(`Error fetching event validation issues: ${error.message}`);
    }
  }
  
  /**
   * Batch fix all missing events
   * This endpoint will create PostgreSQL records for all events that exist in MSSQL but not in PostgreSQL
   */
  /**
   * Process start and end dates from Azure event data
   * This function handles various date/time scenarios and edge cases
   * Enhanced with regex pattern matching to properly handle timezone issues
   */
  private processEventDates(azureEvent: any): { startDateTime: string, endDateTime: string } {
    this.logger.log(`Processing dates for event: ${azureEvent.Name || 'Unknown'} (${azureEvent.Id})`);
    let startDateTime: string, endDateTime: string;
    
    // Use string representations to avoid timezone conversion issues
    if (azureEvent.EventStartDateStr) {
      const startDateStr = azureEvent.EventStartDateStr; // Already in 'YYYY-MM-DD' format
      
      let startTimeStr = '12:00:00'; // Default to noon
      if (azureEvent.EventStartTimeStr) {
        startTimeStr = azureEvent.EventStartTimeStr;
        this.logger.log(`Using string time directly: ${startTimeStr}`);
      } else if (azureEvent.EventStartTime) {
        // Fallback to original Date object processing if string not available
        if (typeof azureEvent.EventStartTime === 'string') {
          const timeMatch = azureEvent.EventStartTime.match(/^(\d{2}):(\d{2}):(\d{2})/);
          if (timeMatch) {
            startTimeStr = `${timeMatch[1]}:${timeMatch[2]}:${timeMatch[3]}`;
            this.logger.log(`Extracted start time ${startTimeStr} using regex`);
          } else {
            startTimeStr = azureEvent.EventStartTime.slice(0, 8);
            this.logger.log(`Fallback start time extraction: ${startTimeStr}`);
          }
        } else if (azureEvent.EventStartTime instanceof Date) {
          const hours = azureEvent.EventStartTime.getHours();
          const minutes = azureEvent.EventStartTime.getMinutes();
          const seconds = azureEvent.EventStartTime.getSeconds();
          startTimeStr = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
          this.logger.log(`Extracted start time ${startTimeStr} from Date object`);
        }
      }
      
      startDateTime = `${startDateStr} ${startTimeStr}`;
      this.logger.log(`Constructed start datetime: ${startDateTime}`);
    } else if (azureEvent.EventStartDate) {
      // Fallback to Date object processing if string not available
      this.logger.log('WARNING: Using Date object fallback for start date');
      const year = azureEvent.EventStartDate.getFullYear();
      const month = String(azureEvent.EventStartDate.getMonth() + 1).padStart(2, '0');
      const day = String(azureEvent.EventStartDate.getDate()).padStart(2, '0');
      startDateTime = `${year}-${month}-${day} 12:00:00`;
    } else {
      // Last resort fallback
      this.logger.log('WARNING: No start date found, using current date');
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const day = String(today.getDate()).padStart(2, '0');
      startDateTime = `${year}-${month}-${day} 12:00:00`;
    }
    
    // Handle end date/time with similar approach
    if (azureEvent.EventEndDateStr) {
      const endDateStr = azureEvent.EventEndDateStr; // Already in 'YYYY-MM-DD' format
      
      let endTimeStr = '23:59:00'; // Default to end of day
      if (azureEvent.EventEndTimeStr) {
        endTimeStr = azureEvent.EventEndTimeStr;
        this.logger.log(`Using string end time directly: ${endTimeStr}`);
      } else if (azureEvent.EventEndTime) {
        // Fallback to original Date object processing if string not available
        if (typeof azureEvent.EventEndTime === 'string') {
          const timeMatch = azureEvent.EventEndTime.match(/^(\d{2}):(\d{2}):(\d{2})/);
          if (timeMatch) {
            endTimeStr = `${timeMatch[1]}:${timeMatch[2]}:${timeMatch[3]}`;
            this.logger.log(`Extracted end time ${endTimeStr} using regex`);
          } else {
            endTimeStr = azureEvent.EventEndTime.slice(0, 8);
            this.logger.log(`Fallback end time extraction: ${endTimeStr}`);
          }
        } else if (azureEvent.EventEndTime instanceof Date) {
          const hours = azureEvent.EventEndTime.getHours();
          const minutes = azureEvent.EventEndTime.getMinutes();
          const seconds = azureEvent.EventEndTime.getSeconds();
          endTimeStr = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
          this.logger.log(`Extracted end time ${endTimeStr} from Date object`);
        }
      }
      
      endDateTime = `${endDateStr} ${endTimeStr}`;
      this.logger.log(`Constructed end datetime: ${endDateTime}`);
    } else {
      // If no end date, use start date with end of day time
      const startDateParts = startDateTime.split(' ')[0];
      endDateTime = `${startDateParts} 23:59:00`;
      this.logger.log(`No end date found, using start date with end of day: ${endDateTime}`);
    }
    
    return { startDateTime, endDateTime };
  }
  
  @Post('batch-fix-events')
  async batchFixEvents() {
    this.logger.log('Starting batch fix for missing events');
    
    try {
      // Get all missing events
      const issues = await this.getMissingEvents();
      
      // Filter to only include missing_postgres issues
      const missingPostgresIssues = issues.filter(issue => issue.issueType === 'missing_postgres');
      
      this.logger.log(`Found ${missingPostgresIssues.length} events missing in PostgreSQL to fix`);
      
      // Initialize counters for the response
      const result = {
        totalIssues: missingPostgresIssues.length,
        processedCount: 0,
        successCount: 0,
        errorCount: 0,
        errors: [],
        fixedEvents: []
      };
      
      // Connect to MSSQL to get event details
      const sql = require('mssql');
      const pool = await this.checkAzureDbConnection();
      
      try {
        // Process each missing event
        for (const issue of missingPostgresIssues) {
          let queryRunner = null;
          try {
            result.processedCount++;
            
            if (!issue.azureRecord || !issue.azureRecord.id) {
              throw new Error(`Missing Azure ID for event: ${issue.description}`);
            }
            
            // Get full event details from MSSQL
            const azureId = issue.azureRecord.id;
            const eventQuery = `
              SELECT 
                Id, Name, Description, VenueId, EventStartDate, EventEndDate, 
                EventStartTime, EventEndTime, CreatedOn,
                CONVERT(varchar, EventStartDate, 23) AS EventStartDateStr,
                CONVERT(varchar, EventStartTime, 8) AS EventStartTimeStr,
                CONVERT(varchar, EventEndDate, 23) AS EventEndDateStr,
                CONVERT(varchar, EventEndTime, 8) AS EventEndTimeStr
              FROM PerformanceEvents 
              WHERE Id = @azureId
            `;
            
            const request = pool.request();
            request.input('azureId', sql.UniqueIdentifier, azureId);
            const eventResult = await request.query(eventQuery);
            
            if (!eventResult.recordset || eventResult.recordset.length === 0) {
              throw new Error(`Event not found in MSSQL: ${azureId}`);
            }
            
            const azureEvent = eventResult.recordset[0];
            
            // Create a new event in PostgreSQL
            queryRunner = this.dataSource.createQueryRunner();
            await queryRunner.connect();
            await queryRunner.startTransaction();
            
            try {
              // Use the existing date processing function from the migration code
              // This function is designed to handle the specific time format issues we're seeing
              const { startDateTime, endDateTime } = this.processEventDates(azureEvent);
              this.logger.log(`Processed dates: Start=${startDateTime}, End=${endDateTime}`);
              
              // If we still don't have valid dates after processing, use defaults
              if (!startDateTime) {
                this.logger.warn(`No valid start date could be processed, skipping event ${azureEvent.Id}`);
                throw new Error('Missing required start date');
              }

              // Check for potential duplicates by name and date before creating a new record
              // This helps prevent creating multiple entries for the same real-world event
              // CRITICAL FIX: Use the same duplicate detection logic as the duplicate-detection.controller.ts
              // to ensure consistency between ID validation and duplicate detection systems
              
              // First, find all possible venue IDs that could match this event's venue
              // This handles cases where there are duplicate venue records for the same real venue
              let venueIds = [];
              
              if (azureEvent.VenueId && azureEvent.VenueId !== '' && azureEvent.VenueId !== 'null') {
                // Try with UUID casting first, fallback to string comparison if needed
                try {
                  venueIds = await queryRunner.query(`
                    SELECT DISTINCT id
                    FROM venue
                    WHERE 
                      deleted IS NOT TRUE
                      AND (
                        azure_id::text = $1
                        OR LOWER(TRIM(name)) IN (
                          SELECT LOWER(TRIM(name))
                          FROM venue
                          WHERE azure_id::text = $1 AND deleted IS NOT TRUE
                        )
                      )
                  `, [azureEvent.VenueId]);
                } catch (error) {
                  this.logger.warn(`Error in venue lookup for ${azureEvent.VenueId}: ${error.message}, using fallback`);
                  // Fallback to all venues if lookup fails
                  venueIds = await queryRunner.query(`
                    SELECT DISTINCT id FROM venue WHERE deleted IS NOT TRUE
                  `);
                }
              } else {
                // If no venue ID, just get all venues (fallback for safety)
                venueIds = await queryRunner.query(`
                  SELECT DISTINCT id FROM venue WHERE deleted IS NOT TRUE
                `);
              }
              
              const venueIdList = venueIds.map(v => v.id);
              
              // Now check for duplicates using the EXACT same logic as duplicate-detection.controller.ts
              // This ensures that we find all potential duplicates that the duplicate detector would find
              const existingEventCheck = await queryRunner.query(`
                SELECT id, name, azure_id, "startDateTime", venue_id
                FROM event
                WHERE 
                  deleted IS NOT TRUE
                  AND name = $1
                  AND venue_id = ANY($2::uuid[])
                  AND "startDateTime" = $3
              `, [azureEvent.Name, venueIdList, startDateTime]);
              
              if (existingEventCheck.length > 0) {
                // Found potential duplicate(s)
                const duplicate = existingEventCheck[0];
                this.logger.log(
                  `Found potential duplicate for Azure event ${azureEvent.Id} (${azureEvent.Name}): ${duplicate.id} (${duplicate.name})`
                );
                
                // If the existing event doesn't have an Azure ID, update it with this Azure ID
                if (!duplicate.azure_id) {
                  await queryRunner.query(`
                    UPDATE event
                    SET 
                      azure_id = $1,
                      updated_at = NOW(),
                      id_validated = TRUE,
                      dedup_validated = TRUE, 
                      last_validation_date = NOW(),
                      validation_version = COALESCE(validation_version, 0) + 1
                    WHERE id = $2
                  `, [azureEvent.Id, duplicate.id]);
                  
                  result.successCount++;
                  result.fixedEvents.push({
                    id: duplicate.id,
                    name: duplicate.name,
                    azureId: azureEvent.Id,
                    message: 'Updated existing event with Azure ID'
                  });
                  
                  this.logger.log(`Updated existing event ${duplicate.id} (${duplicate.name}) with Azure ID ${azureEvent.Id}`);
                  await queryRunner.commitTransaction();
                  continue;
                }
              }
              
              // If no duplicates found or the duplicate handling is done, proceed with insert
              // Insert the event into PostgreSQL
              const insertResult = await queryRunner.query(`
                INSERT INTO event (
                  id, azure_id, name, description, "startDateTime", "endDateTime", venue_id, 
                  created_at, updated_at, deleted, id_validated, dedup_validated, last_validation_date, validation_version
                ) VALUES (
                  uuid_generate_v4(), $1, $2, $3, $4, $5, 
                  (SELECT id FROM venue WHERE azure_id::text = $6 LIMIT 1),
                  NOW(), NOW(), FALSE, TRUE, TRUE, NOW(), 1
                ) RETURNING id
              `, [
                azureEvent.Id,
                azureEvent.Name,
                azureEvent.Description || '',
                startDateTime,
                endDateTime,
                azureEvent.VenueId
              ]);
              
              await queryRunner.commitTransaction();
              
              // Add to success count and fixed events list
              result.successCount++;
              result.fixedEvents.push({
                id: insertResult[0].id,
                name: azureEvent.Name,
                azureId: azureEvent.Id
              });
              
              this.logger.log(`Successfully created event in PostgreSQL: ${azureEvent.Name} (${azureEvent.Id})`);
            } catch (transactionError) {
              try {
                if (queryRunner) {
                  await queryRunner.rollbackTransaction();
                }
              } catch (rollbackError) {
                this.logger.error(`Error during rollback: ${rollbackError.message}`);
              }
              throw transactionError;
            } finally {
              try {
                if (queryRunner) {
                  await queryRunner.release();
                }
              } catch (releaseError) {
                this.logger.error(`Error releasing query runner: ${releaseError.message}`);
              }
            }
          } catch (eventError) {
            result.errorCount++;
            result.errors.push({
              event: issue.azureRecord?.name || 'Unknown',
              error: eventError.message
            });
            this.logger.error(`Error processing event ${issue.azureRecord?.id}: ${eventError.message}`);
            this.logger.error(`Error stack: ${eventError.stack}`);
            
            // Make sure to clean up the query runner if it exists but wasn't cleaned up in finally
            if (queryRunner) {
              try {
                if (queryRunner.isTransactionActive) {
                  await queryRunner.rollbackTransaction();
                }
                if (!queryRunner.isReleased) {
                  await queryRunner.release();
                }
              } catch (cleanupError) {
                this.logger.error(`Error during cleanup: ${cleanupError.message}`);
              }
            }
          }
        }
      } finally {
        // Close the MSSQL connection
        if (pool) {
          try {
            await pool.close();
            this.logger.log('MSSQL connection closed');
          } catch (closeError) {
            this.logger.error(`Error closing MSSQL connection: ${closeError.message}`);
          }
        }
      }
      
      this.logger.log(`Batch fix completed: ${result.successCount} events fixed, ${result.errorCount} errors`);
      return result;
    } catch (error) {
      this.logger.error(`Error in batch fix: ${error.message}`);
      throw new InternalServerErrorException(`Error in batch fix: ${error.message}`);
    }
  }
  
  @Get('counts')
  async getValidationCounts(): Promise<ValidationCounts> {
    try {
      await this.checkAzureDbConnection();
      
      // Count of venues with missing Azure IDs
      const venuesMissingAzureQuery = `
        SELECT COUNT(*) as count 
        FROM venue 
        WHERE deleted = FALSE
        AND (azure_id IS NULL OR azure_id = '')
      `;
      
      // Count of musicians with missing Azure IDs
      const musiciansMissingAzureQuery = `
        SELECT COUNT(*) as count 
        FROM talent 
        WHERE deleted = FALSE
        AND (azure_id IS NULL OR azure_id = '')
      `;
      
      const [venueResult, musicianResult] = await Promise.all([
        this.dataSource.query(venuesMissingAzureQuery),
        this.dataSource.query(musiciansMissingAzureQuery),
      ]);
      
      // For events, use the EXACT same method to count as we use to fetch issues
      // This ensures perfect consistency between the count and actual issues
      let eventCount = 0;
      try {
        // The key fix: instead of doing a separate count calculation, 
        // directly use the getMissingEvents method which is used for fetching issues
        const issues = await this.getMissingEvents();
        eventCount = issues.length;
        
        // Log details about each event issue for debugging
        this.logger.log(`Found exactly ${issues.length} event validation issues using getMissingEvents()`);
        issues.forEach(issue => {
          this.logger.log(`- Issue: ${issue.description}`);
        });
      } catch (missingError) {
        this.logger.error(`Error counting missing events: ${missingError.message}`);
      }
      
      return {
        venueCount: venueResult[0].count,
        musicianCount: musicianResult[0].count,
        eventCount: eventCount
      };
    } catch (error) {
      this.logger.error(`Error fetching validation counts: ${error.message}`);
      throw new InternalServerErrorException(`Error fetching validation counts: ${error.message}`);
    }
  }

  /**
   * Get an Azure record by ID
   */
  @Get('azure-records/:entityType/:azureId')
  async getAzureRecordById(
    @Param('entityType') entityType: string,
    @Param('azureId') azureId: string,
  ) {
    const sql = require('mssql');
    let pool = null;
    
    try {
      this.logger.log(`Fetching Azure ${entityType} record with ID: ${azureId}`);
      
      // Format validation for Azure ID
      if (!azureId || azureId.trim() === '') {
        this.logger.error('Invalid Azure ID: Empty or undefined');
        throw new NotFoundException('Invalid Azure ID: Empty or undefined');
      }
      
      // Clean up the ID - remove any whitespace that might have been introduced
      const cleanAzureId = azureId.trim();
      
      this.logger.log(`Cleaned Azure ID format: ${cleanAzureId}`);
      
      // In the production implementation, we would validate this ID against the Azure database
      // For now, we'll accept Azure IDs in various formats
      // Microsoft sometimes uses different GUID formats including some with additional segments
      // This regex is more permissive to handle various Microsoft ID formats
      const isValidFormat = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}(?:-[0-9a-f]+)*$/i.test(cleanAzureId);
      
      this.logger.log(`Azure ID format validation: ${isValidFormat ? 'VALID' : 'INVALID'} - ${cleanAzureId}`);
      
      if (!isValidFormat) {
        this.logger.error(`Invalid Azure ID format: ${cleanAzureId}`);
        throw new NotFoundException(`Invalid Azure ID format: ${cleanAzureId}`);
      }
      
      // Connect to Azure SQL and fetch real data
      try {
        // Get a connection pool
        pool = await this.checkAzureDbConnection();
        
        if (entityType === 'events') {
          // Query event data using the ID
          const result = await pool.request()
            .input('eventId', sql.UniqueIdentifier, cleanAzureId)
            .query(`
              SELECT 
                Id, 
                Name, 
                Description,
                EventStartDate,
                EventStartTime,
                EventEndDate,
                EventEndTime,
                CONVERT(varchar, EventStartDate, 23) AS EventStartDateStr,
                CONVERT(varchar, EventStartTime, 8) AS EventStartTimeStr,
                CONVERT(varchar, EventEndDate, 23) AS EventEndDateStr,
                CONVERT(varchar, EventEndTime, 8) AS EventEndTimeStr,
                VenueId,
                FeatureLevel,
                ProfileImageCropperValue,
                ContactName,
                ContactEmailAddress,
                ContactPhoneNumber,
                ContactWebsite,
                Locale,
                Url,
                CreatedOn,
                UpdatedOn
              FROM PerformanceEvents 
              WHERE Id = @eventId
            `);
          
          if (result.recordset.length === 0) {
            throw new NotFoundException(`Event with ID ${cleanAzureId} not found in Azure`); 
          }
          
          const azureEvent = result.recordset[0];
          
          // Combine date and time fields to create proper DateTime strings
          let startDateTime = null;
          let endDateTime = null;
          
          try {
            // Use the string representations to avoid timezone conversion
            if (azureEvent.EventStartDateStr) {
              const startDateStr = azureEvent.EventStartDateStr; // Already in 'YYYY-MM-DD' format
              
              let startTimeStr = '00:00:00';
              if (azureEvent.EventStartTimeStr) {
                startTimeStr = azureEvent.EventStartTimeStr;
              }
              
              // Create a direct datetime string (no timezone conversion)
              const startDateTimeStr = `${startDateStr}T${startTimeStr}`;
              startDateTime = new Date(startDateTimeStr);
              this.logger.log(`Processed start date/time: ${startDateTimeStr}`);
            }
            
            // Same approach for end date/time
            if (azureEvent.EventEndDateStr) {
              const endDateStr = azureEvent.EventEndDateStr; // Already in 'YYYY-MM-DD' format
              
              let endTimeStr = '00:00:00';
              if (azureEvent.EventEndTimeStr) {
                endTimeStr = azureEvent.EventEndTimeStr;
              }
              
              const endDateTimeStr = `${endDateStr}T${endTimeStr}`;
              endDateTime = new Date(endDateTimeStr);
              this.logger.log(`Processed end date/time: ${endDateTimeStr}`);
            } else if (azureEvent.EventStartDateStr && azureEvent.EventEndTimeStr) {
              // If only end time is available, use start date with end time
              const endDateStr = azureEvent.EventStartDateStr;
              const endTimeStr = azureEvent.EventEndTimeStr;
              
              const endDateTimeStr = `${endDateStr}T${endTimeStr}`;
              endDateTime = new Date(endDateTimeStr);
            }
          } catch (error) {
            this.logger.error(`Error processing event date/time: ${error.message}`);
          }
          
          // Extract image URL from ProfileImageCropperValue
          let imageUrl = '';
          if (azureEvent.ProfileImageCropperValue && typeof azureEvent.ProfileImageCropperValue === 'string') {
            try {
              const imageData = JSON.parse(azureEvent.ProfileImageCropperValue);
              if (imageData && imageData.Src) {
                const imagePath = imageData.Src;
                imageUrl = imagePath.startsWith('http') 
                  ? imagePath 
                  : `https://tucsonlovesmusic.com${imagePath}`;
              }
            } catch (e) {
              this.logger.error(`Error parsing event ProfileImageCropperValue JSON: ${e.message}`);
            }
          }
          
          return {
            id: azureEvent.Id,
            name: azureEvent.Name || 'Unknown Event',
            description: azureEvent.Description || '',
            startDateTime: azureEvent.EventStartDateStr ? `${azureEvent.EventStartDateStr} ${azureEvent.EventStartTimeStr || '00:00:00'}` : null,
            endDateTime: azureEvent.EventEndDateStr ? `${azureEvent.EventEndDateStr} ${azureEvent.EventEndTimeStr || '00:00:00'}` : null,
            venueId: azureEvent.VenueId,
            imageUrl: imageUrl,
            featured: azureEvent.FeatureLevel > 0,

            url: azureEvent.Url
          };
        } else if (entityType === 'venues') {
          // Query venue data using the ID
          const result = await pool.request()
            .input('venueId', sql.UniqueIdentifier, cleanAzureId)
            .query(`
              SELECT 
                Id, 
                Name, 
                ContactWebsite as Website, 
                MapLocation, 
                Locale,
                CreatedOn,
                UpdatedOn
              FROM Venues 
              WHERE Id = @venueId
            `);
          
          if (result.recordset.length === 0) {
            throw new NotFoundException(`Venue with ID ${cleanAzureId} not found in Azure`); 
          }
          
          const venue = result.recordset[0];
          
          // Process Locale field if it exists (it's stored as a JSON string)
          let locale = null;
          let address = '';
          let city = '';
          let state = '';
          let zipCode = '';
          let googlePlaceId = '';
          let latitude = null;
          let longitude = null;
          
          // First try to extract from Locale field (this is the preferred source)
          if (venue.Locale) {
            try {
              // Try to parse Locale as JSON if it's a string
              if (typeof venue.Locale === 'string') {
                locale = JSON.parse(venue.Locale);
              } else {
                locale = venue.Locale;
              }
              
              // Extract address data from Locale.MapLocation.address
              if (locale?.MapLocation?.address) {
                const localeAddress = locale.MapLocation.address;
                
                if (localeAddress.full_address) {
                  address = localeAddress.full_address;
                  this.logger.log(`Found address in Locale: ${address}`);
                }
                
                if (localeAddress.city) {
                  city = localeAddress.city;
                }
                
                if (localeAddress.state) {
                  state = localeAddress.state;
                }
                
                if (localeAddress.postalcode) {
                  zipCode = localeAddress.postalcode;
                }
                
                // Get coordinates
                if (localeAddress.coordinates && !localeAddress.coordinates.IsEmpty) {
                  latitude = localeAddress.coordinates.lat;
                  longitude = localeAddress.coordinates.lng;
                }
              }
            } catch (error) {
              this.logger.warn(`Failed to parse Locale: ${error.message}`);
            }
          }
          
          // If we didn't get an address from Locale, try MapLocation as fallback
          if (!address && venue.MapLocation) {
            try {
              // Try to parse as JSON if it's a string
              let mapLocationObj;
              if (typeof venue.MapLocation === 'string') {
                mapLocationObj = JSON.parse(venue.MapLocation);
              } else {
                mapLocationObj = venue.MapLocation;
              }
              
              // Extract address info if it exists in the expected structure
              if (mapLocationObj.address) {
                // Address could be in different formats
                if (mapLocationObj.address.full_address) {
                  address = mapLocationObj.address.full_address;
                  this.logger.log(`Found address in MapLocation: ${address}`);
                }
                
                if (!city && mapLocationObj.address.city) {
                  city = mapLocationObj.address.city;
                }
                
                if (!state && mapLocationObj.address.state) {
                  state = mapLocationObj.address.state;
                }
                
                if (!zipCode && mapLocationObj.address.postalcode) {
                  zipCode = mapLocationObj.address.postalcode;
                }
                
                // Get coordinates if not already set
                if (!latitude && !longitude && mapLocationObj.address.coordinates && !mapLocationObj.address.coordinates.IsEmpty) {
                  latitude = mapLocationObj.address.coordinates.lat;
                  longitude = mapLocationObj.address.coordinates.lng;
                }
              }
            } catch (error) {
              this.logger.warn(`Failed to parse MapLocation: ${error.message}`);
            }
          }
          
          // Fallback to direct fields if no address data found yet
          if (!address && venue.Address) {
            address = venue.Address;
          }
          
          if (!city && venue.City) {
            city = venue.City;
          }
          
          if (!state && venue.State) {
            state = venue.State;
          }
          
          // Return formatted venue data including the full Locale object
          return {
            Id: venue.Id,
            Name: venue.Name || 'Unknown Venue',
            Address: address,
            City: city || '',
            State: state || '',
            Zip: zipCode || '',
            Website: venue.Website || '',
            GooglePlaceId: googlePlaceId || '',
            Latitude: latitude,
            Longitude: longitude,
            CreatedAt: venue.CreatedOn ? new Date(venue.CreatedOn).toISOString() : new Date().toISOString(),
            UpdatedAt: venue.UpdatedOn ? new Date(venue.UpdatedOn).toISOString() : new Date().toISOString(),
            // Include the full Locale object for the frontend to use
            Locale: locale
          };
        } else if (entityType === 'musicians') {
          // Query musician data using the ID with all relevant fields
          const result = await pool.request()
            .input('talentId', sql.UniqueIdentifier, cleanAzureId)
            .query(`
              SELECT 
                Id, 
                Name, 
                Description as Bio,
                FeatureLevel as Category,
                ContactName,
                ContactEmailAddress as Email,
                ContactPhoneNumber as PhoneNumber,
                ContactWebsite as Website,
                ProfileImageCropperValue,
                ProfileImageId,
                Url,
                CreatedOn as CreatedAt,
                UpdatedOn as UpdatedAt
              FROM Talent 
              WHERE Id = @talentId
            `);
          
          if (result.recordset.length === 0) {
            throw new NotFoundException(`Musician with ID ${cleanAzureId} not found in Azure`); 
          }
          
          const musician = result.recordset[0];
          
          // Process image data from ProfileImageCropperValue
          let imageUrl = '';
          if (musician.ProfileImageCropperValue && typeof musician.ProfileImageCropperValue === 'string') {
            try {
              const imageData = JSON.parse(musician.ProfileImageCropperValue);
              this.logger.log('Parsed ProfileImageCropperValue successfully');
              
              // Extract image URL from Src property
              if (imageData && imageData.Src) {
                // Create full URL by adding base domain if it's a relative path
                const imagePath = imageData.Src;
                this.logger.log(`Found image path: ${imagePath}`);
                
                // Store the original image path from Umbraco
                imageUrl = imagePath.startsWith('http') 
                  ? imagePath 
                  : `https://tucsonlovesmusic.com${imagePath}`;
                
                // Also include the raw image data
                musician.ImageData = imageData;
              }
            } catch (e) {
              this.logger.error(`Error parsing musician ProfileImageCropperValue JSON: ${e.message}`);
            }
          }
          
          // Get genre from EntertainmentGenres relation or set default
          const genre = 'Unknown'; // We'll need to query the related genres table in the future
          
          return {
            Id: musician.Id,
            Name: musician.Name || 'Unknown Musician',
            Bio: musician.Bio || '',
            Category: musician.Category || '',
            Genre: genre,
            ContactName: musician.ContactName || '',
            Email: musician.Email || '',
            PhoneNumber: musician.PhoneNumber || '',
            Website: musician.Website || '',
            ImageUrl: imageUrl,
            Url: musician.Url || '',
            CreatedAt: musician.CreatedAt ? new Date(musician.CreatedAt).toISOString() : new Date().toISOString(),
            UpdatedAt: musician.UpdatedAt ? new Date(musician.UpdatedAt).toISOString() : new Date().toISOString(),
            ImageData: musician.ImageData || null,
            ProfileImageId: musician.ProfileImageId || null
          };
        } else if (entityType === 'events') {
          // Query event data using the ID with more comprehensive fields
          const result = await pool.request()
            .input('eventId', sql.UniqueIdentifier, cleanAzureId)
            .query(`
              SELECT 
                Id, 
                Name, 
                Description, 
                EventStartDate,
                EventStartTime,
                EventEndDate,
                EventEndTime,
                CONVERT(varchar, EventStartDate, 23) AS EventStartDateStr,
                CONVERT(varchar, EventStartTime, 8) AS EventStartTimeStr,
                CONVERT(varchar, EventEndDate, 23) AS EventEndDateStr,
                CONVERT(varchar, EventEndTime, 8) AS EventEndTimeStr,
                VenueId,
                FeatureLevel,
                ProfileImageId,
                ProfileImageCropperValue,
                ContactName,
                ContactEmailAddress,
                ContactPhoneNumber,
                ContactWebsite,
                CreatedOn,
                UpdatedOn
              FROM PerformanceEvents 
              WHERE Id = @eventId
            `);
          
          if (result.recordset.length === 0) {
            throw new NotFoundException(`Event with ID ${cleanAzureId} not found in Azure`); 
          }
          
          const event = result.recordset[0];
          
          // Combine date and time fields to create proper DateTime strings using string values
          const startDateTime = this.combineDateTime(event.EventStartDateStr, event.EventStartTimeStr);
          const endDateTime = this.combineDateTime(event.EventEndDateStr, event.EventEndTimeStr);
          
          // Extract image data if available
          let imageUrl = '';
          let cropData = null;
          if (event.ProfileImageCropperValue && typeof event.ProfileImageCropperValue === 'string') {
            try {
              const imageData = JSON.parse(event.ProfileImageCropperValue);
              
              // Extract image URL from Src property if it exists
              if (imageData && imageData.Src) {
                // Create full URL by adding base domain if it's a relative path
                const imagePath = imageData.Src;
                imageUrl = imagePath.startsWith('http') 
                  ? imagePath 
                  : `https://tucsonlovesmusic.com${imagePath}`;
                
                // Store the crop data
                cropData = imageData;
              }
            } catch (e) {
              this.logger.error(`Error parsing event ProfileImageCropperValue JSON: ${e.message}`);
            }
          }

          return {
            Id: event.Id,
            Name: event.Name || 'Unknown Event',
            Description: event.Description || '',
            StartDateTime: startDateTime,
            EndDateTime: endDateTime,
            VenueId: event.VenueId,
            ContactName: event.ContactName || '',
            ContactEmail: event.ContactEmailAddress || '',
            ContactPhone: event.ContactPhoneNumber || '',
            ContactWebsite: event.ContactWebsite || '',
            FeatureLevel: event.FeatureLevel || 0,
            ImageUrl: imageUrl,
            ProfileImageId: event.ProfileImageId,
            CropData: cropData,
            CreatedAt: event.CreatedOn ? new Date(event.CreatedOn).toISOString() : new Date().toISOString(),
            UpdatedAt: event.UpdatedOn ? new Date(event.UpdatedOn).toISOString() : new Date().toISOString()
          };
        }
        
        throw new NotFoundException(`Unsupported entity type: ${entityType}`);
      } finally {
        // Close the connection pool if it was created
        if (pool) {
          await pool.close();
        }
      }
    } catch (error) {
      this.logger.error(`Error fetching Azure record: ${error.message}`);
      
      // If it's already a NestJS HttpException (like NotFoundException), rethrow it
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      // For other errors, throw a generic server error
      throw new InternalServerErrorException(`Failed to fetch Azure record: ${error.message}`);
    }
  }

  @Get('issues/:type')
  async getValidationIssues(@Param('type') entityType: string): Promise<ValidationIssue[]> {
    if (!['venues', 'musicians', 'events', 'missing_events'].includes(entityType)) {
      throw new NotFoundException(`Invalid entity type: ${entityType}`);
    }
    
    try {
      const issues: ValidationIssue[] = [];
      const azureDbAvailable = await this.checkAzureDbConnection();
      
      if (entityType === 'venues') {
        // Get venues with missing Azure IDs
        const venuesMissingAzure = await this.dataSource.query(`
          SELECT id, name, address, city, state, image_url, created_at, updated_at, website
          FROM venue
          WHERE (azure_id IS NULL OR azure_id = '')
          AND deleted IS NOT TRUE
          LIMIT 100
        `);
        
        // Map to ValidationIssue format
        venuesMissingAzure.forEach(venue => {
          issues.push({
            id: venue.id,
            entityType: 'venues',
            postgresRecord: venue,
            azureRecord: null,
            issueType: 'missing_azure',
            description: `Venue "${venue.name}" exists in PostgreSQL but has no Azure ID`,
            createdAt: new Date().toISOString()
          });
        });
        
        // Look for Azure venues not in PostgreSQL (mock implementation for now)
        if (azureDbAvailable) {
          this.logger.log('Checking for Azure venues not in PostgreSQL');
          // In a real implementation, we would query the Azure SQL database here
          // and compare with PostgreSQL records
          
          // For now, just show an example of how this would work with mock data
          /* 
          const mockAzureOnlyVenues = [
            {
              Id: '12345678-1234-1234-1234-123456789012',
              Name: 'Some Azure-Only Venue',
              Description: 'This venue exists only in Azure',
              CreatedOn: new Date('2024-01-01').toISOString()
            }
          ];
          
          mockAzureOnlyVenues.forEach(azureVenue => {
            issues.push({
              id: azureVenue.Id,
              entityType: 'venues',
              postgresRecord: null,
              azureRecord: azureVenue,
              issueType: 'missing_postgres',
              description: `Venue "${azureVenue.Name}" exists in Azure but not in PostgreSQL`,
              createdAt: new Date().toISOString()
            });
          });
          */
        }
      }
      else if (entityType === 'musicians') {
        try {
          // Get musicians with missing Azure IDs
          const musiciansMissingAzure = await this.dataSource.query(`
            SELECT id, name, bio, category, genre, "imageUrl", "createdAt", "updatedAt", website, email, "phoneNumber", "socialMedia"
            FROM talent
            WHERE (azure_id IS NULL OR azure_id = '')
            AND deleted IS NOT TRUE
            LIMIT 100
          `);
          
          // Map to ValidationIssue format
          musiciansMissingAzure.forEach(musician => {
            issues.push({
              id: musician.id,
              entityType: 'musicians',
              postgresRecord: {
                id: musician.id,
                name: musician.name,
                bio: musician.bio,
                category: musician.category,
                genre: musician.genre,
                imageUrl: musician.imageUrl,
                email: musician.email,
                phoneNumber: musician.phoneNumber,
                website: musician.website,
                socialMedia: musician.socialMedia,
                createdAt: musician.createdAt,
                updatedAt: musician.updatedAt
              },
              azureRecord: null,
              issueType: 'missing_azure',
              description: `Musician "${musician.name}" exists in PostgreSQL but has no Azure ID`,
              createdAt: new Date().toISOString()
            });
          });
          
          // Look for Azure musicians not in PostgreSQL
          if (azureDbAvailable) {
            this.logger.log('Checking for Azure musicians not in PostgreSQL');
            // In a real implementation, we would query the Azure SQL database here
            // and compare with PostgreSQL records
          }
        } catch (musicianError) {
          this.logger.error(`Error fetching musicians issues: ${musicianError.message}`);
          throw new InternalServerErrorException(`Error fetching musicians issues: ${musicianError.message}`);
        }
      }
      else if (entityType === 'events') {
        try {
          // Get events with missing Azure IDs
          const eventsMissingAzure = await this.dataSource.query(`
            SELECT e.id, e.name, e."imageUrl", 
                  e."startDateTime", e."endDateTime",
                  e.created_at, e.updated_at, 
                  v.name as venue_name, v.id as venue_id
            FROM event e
            LEFT JOIN venue v ON e.venue_id = v.id
            WHERE e.deleted = FALSE
            AND (e.azure_id IS NULL OR e.azure_id = '')
            LIMIT 100
          `);
          
          // Map to ValidationIssue format
          eventsMissingAzure.forEach(event => {
            issues.push({
              id: event.id,
              entityType: 'events',
              postgresRecord: event,
              azureRecord: null,
              issueType: 'missing_azure',
              description: `Event "${event.name}" at ${event.venue_name || 'Unknown Venue'} exists in PostgreSQL but has no Azure ID`,
              createdAt: new Date().toISOString()
            });
          });
          
          // Always check for events in MSSQL that are missing from PostgreSQL
          // This will show the 87 events that are only in source (MSSQL)
          try {
            const missingEvents = await this.getMissingEvents();
            this.logger.log(`Found ${missingEvents.length} events in MSSQL that are missing from PostgreSQL`);
            issues.push(...missingEvents);
          } catch (missingError) {
            this.logger.error(`Error fetching missing events: ${missingError.message}`);
            // Don't throw here, just log the error and continue with the events we have
          }
        } catch (eventError) {
          this.logger.error(`Error fetching events issues: ${eventError.message}`);
          throw new InternalServerErrorException(`Error fetching events issues: ${eventError.message}`);
        }
      }
      else if (entityType === 'missing_events') {
        const missingEvents = await this.getMissingEvents();
        issues.push(...missingEvents);
      }
      
      return issues;
    } catch (error) {
      this.logger.error(`Error fetching ${entityType} issues: ${error.message}`);
      throw new InternalServerErrorException(`Failed to fetch ${entityType} issues: ${error.message}`);
    }
  }

  @Post('update-azure-id')
  async updateAzureId(@Body() data: { tableName: string; recordId: string; azureId: string; mergeFields?: boolean }) {
    this.logger.log(`Updating ${data.tableName} record ${data.recordId} with Azure ID ${data.azureId}`);
    
    try {
      // Validate the table name
      const validTables = ['venue', 'venues', 'talent', 'talents', 'event', 'events', 'musicians'];
      if (!validTables.includes(data.tableName)) {
        throw new BadRequestException(`Invalid table name: ${data.tableName}`);
      }
      
      // Update the PostgreSQL record with the Azure ID
      // Map table names to actual database table names
      let normalizedTable;
      let updatedAtColumn;
      
      if (data.tableName === 'musicians' || data.tableName === 'talent' || data.tableName === 'talents') {
        normalizedTable = 'talent'; // Musicians are stored in the talent table
        updatedAtColumn = '"updatedAt"'; // Talent table uses camelCase column names
      } else {
        normalizedTable = data.tableName.replace(/s$/, ''); // Remove 's' for other tables
        updatedAtColumn = 'updated_at'; // Venue and Event tables use snake_case column names
      }
      
      const updateQuery = `
        UPDATE ${normalizedTable}
        SET azure_id = $1, ${updatedAtColumn} = $2
        WHERE id = $3
        RETURNING id, name, azure_id
      `;
      
      const updateResult = await this.dataSource.query(updateQuery, [
        data.azureId,
        new Date().toISOString(),
        data.recordId
      ]);
      
      if (!updateResult || updateResult.length === 0) {
        throw new NotFoundException(`Record with ID ${data.recordId} not found in table ${data.tableName}`);
      }
      
      return {
        success: true,
        message: `Updated ${data.tableName} record with Azure ID: ${data.azureId}`,
        entity: updateResult[0]
      };
    } catch (error) {
      this.logger.error(`Error updating Azure ID: ${error.message}`);
      throw new InternalServerErrorException(`Failed to update Azure ID: ${error.message}`);
    }
  }
  
  // This endpoint is deprecated - use update-azure-id or resolve/:issueId instead
  
  @Post('create-missing-event')
  async createMissingEvent(@Body() data: { azureId: string }) {
    this.logger.log(`Creating missing event with Azure ID: ${data.azureId}`);
    
    try {
      // Connect to Azure SQL to get event details
      const sql = require('mssql');
      const pool = await this.checkAzureDbConnection();
      
      try {
        // Get the event details from Azure
        const eventQuery = await pool.request()
          .input('eventId', sql.UniqueIdentifier, data.azureId)
          .query(`
            SELECT 
              e.Id, e.Name, e.Description, e.EventStartDate, e.EventStartTime, 
              e.EventEndDate, e.EventEndTime, e.VenueId, e.ProfileImageCropperValue,
              e.Url, e.FeatureLevel, e.CreatedOn, e.UpdatedOn,
              CONVERT(varchar, e.EventStartDate, 23) AS EventStartDateStr,
              CONVERT(varchar, e.EventStartTime, 8) AS EventStartTimeStr,
              CONVERT(varchar, e.EventEndDate, 23) AS EventEndDateStr,
              CONVERT(varchar, e.EventEndTime, 8) AS EventEndTimeStr,
              v.Name as VenueName
            FROM PerformanceEvents e
            LEFT JOIN Venues v ON e.VenueId = v.Id
            WHERE e.Id = @eventId
          `);
        
        if (!eventQuery.recordset || eventQuery.recordset.length === 0) {
          throw new NotFoundException(`Event with Azure ID ${data.azureId} not found`);
        }
        
        const azureEvent = eventQuery.recordset[0];
        this.logger.log(`Found Azure event: ${azureEvent.Name}`);
        
        // Format the dates properly using string values
        const startDateTime = this.combineDateTime(azureEvent.EventStartDateStr, azureEvent.EventStartTimeStr);
        const endDateTime = this.combineDateTime(azureEvent.EventEndDateStr || azureEvent.EventStartDateStr, azureEvent.EventEndTimeStr);
        
        // Look up the venue in PostgreSQL by Azure ID
        let venueId = null;
        let venueName = 'Unknown Venue';
        
        if (azureEvent.VenueId && azureEvent.VenueId !== '' && azureEvent.VenueId !== 'null') {
          // First try to find the venue by Azure ID
          let venueResult = [];
          try {
            const venueQuery = `SELECT id, name FROM venue WHERE azure_id::text = $1 AND deleted = FALSE`;
            venueResult = await this.dataSource.query(venueQuery, [azureEvent.VenueId]);
          } catch (error) {
            this.logger.warn(`Error in venue lookup for ${azureEvent.VenueId}: ${error.message}, trying name-based lookup`);
            venueResult = []; // Empty result to trigger name-based lookup below
          }
          
          if (venueResult && venueResult.length > 0) {
            venueId = venueResult[0].id;
            venueName = venueResult[0].name;
            this.logger.log(`Found matching venue in PostgreSQL with ID: ${venueId}, Name: ${venueName}`);
          } else {
            // If venue not found by Azure ID, try to find by name
            if (azureEvent.VenueName) {
              const venueNameQuery = `SELECT id, name FROM venue WHERE name ILIKE $1 AND deleted = FALSE`;
              const venueNameResult = await this.dataSource.query(venueNameQuery, [`%${azureEvent.VenueName}%`]);
              
              if (venueNameResult && venueNameResult.length > 0) {
                venueId = venueNameResult[0].id;
                venueName = venueNameResult[0].name;
                this.logger.log(`Found venue by name in PostgreSQL with ID: ${venueId}, Name: ${venueName}`);
              } else {
                this.logger.log(`No matching venue found in PostgreSQL for Azure ID: ${azureEvent.VenueId} or name: ${azureEvent.VenueName}`);
                
                // If we have venue name from Azure but no matching venue in PostgreSQL, create a new venue
                if (azureEvent.VenueName) {
                  const createVenueResult = await this.dataSource.query(
                    `INSERT INTO venue (name, azure_id, created_at, updated_at, deleted)
                     VALUES ($1, $2, $3, $4, $5)
                     RETURNING id`,
                    [
                      azureEvent.VenueName,
                      azureEvent.VenueId,
                      new Date().toISOString(),
                      new Date().toISOString(),
                      false
                    ]
                  );
                  
                  if (createVenueResult && createVenueResult.length > 0) {
                    venueId = createVenueResult[0].id;
                    venueName = azureEvent.VenueName;
                    this.logger.log(`Created new venue in PostgreSQL with ID: ${venueId}, Name: ${venueName}`);
                  }
                }
              }
            }
          }
        } else if (azureEvent.VenueName) {
          // If we have venue name but no venue ID, try to find by name
          const venueNameQuery = `SELECT id, name FROM venue WHERE name ILIKE $1 AND deleted = FALSE`;
          const venueNameResult = await this.dataSource.query(venueNameQuery, [`%${azureEvent.VenueName}%`]);
          
          if (venueNameResult && venueNameResult.length > 0) {
            venueId = venueNameResult[0].id;
            venueName = venueNameResult[0].name;
            this.logger.log(`Found venue by name in PostgreSQL with ID: ${venueId}, Name: ${venueName}`);
          } else if (azureEvent.VenueName) {
            // Create a new venue with just the name
            const createVenueResult = await this.dataSource.query(
              `INSERT INTO venue (name, created_at, updated_at, deleted)
               VALUES ($1, $2, $3, $4)
               RETURNING id`,
              [
                azureEvent.VenueName,
                new Date().toISOString(),
                new Date().toISOString(),
                false
              ]
            );
            
            if (createVenueResult && createVenueResult.length > 0) {
              venueId = createVenueResult[0].id;
              venueName = azureEvent.VenueName;
              this.logger.log(`Created new venue in PostgreSQL with ID: ${venueId}, Name: ${venueName}`);
            }
          }
        }
        
        // Extract image URL from ProfileImageCropperValue
        let imageUrl = '';
        if (azureEvent.ProfileImageCropperValue && typeof azureEvent.ProfileImageCropperValue === 'string') {
          try {
            const imageData = JSON.parse(azureEvent.ProfileImageCropperValue);
            if (imageData && imageData.Src) {
              const imagePath = imageData.Src;
              imageUrl = imagePath.startsWith('http') 
                ? imagePath 
                : `https://tucsonlovesmusic.com${imagePath}`;
            }
          } catch (e) {
            this.logger.error(`Error parsing ProfileImageCropperValue: ${e.message}`);
          }
        }
        
        // Create the event in PostgreSQL
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        
        try {
          // CRITICAL FIX: Check for duplicates before creating event
          // Use the same logic as batch-fix-events to prevent duplicate creation
          
          // First, find all possible venue IDs that could match this event's venue
          // This handles cases where there are duplicate venue records for the same real venue
          let venueIds = [];
          
          if (venueId || (azureEvent.VenueId && azureEvent.VenueId !== '' && azureEvent.VenueId !== 'null')) {
            try {
              const conditions = [];
              const params = [];
              
              if (venueId) {
                conditions.push('id = $' + (params.length + 1));
                params.push(venueId);
              }
              
              if (azureEvent.VenueId && azureEvent.VenueId !== '' && azureEvent.VenueId !== 'null') {
                conditions.push('(azure_id::text = $' + (params.length + 1) + ' AND azure_id IS NOT NULL)');
                params.push(azureEvent.VenueId);
                
                conditions.push(`LOWER(TRIM(name)) IN (
                  SELECT LOWER(TRIM(name))
                  FROM venue
                  WHERE azure_id::text = $${params.length} AND deleted IS NOT TRUE
                )`);
              }
              
              venueIds = await queryRunner.query(`
                SELECT DISTINCT id
                FROM venue
                WHERE 
                  deleted IS NOT TRUE
                  AND (${conditions.join(' OR ')})
              `, params);
            } catch (error) {
              this.logger.warn(`Error in venue lookup for ${azureEvent.VenueId}: ${error.message}, using fallback`);
              // Fallback to all venues if UUID is invalid
              venueIds = await queryRunner.query(`
                SELECT DISTINCT id FROM venue WHERE deleted IS NOT TRUE
              `);
            }
          } else {
            // If no venue info, just use all venues (fallback for safety)
            venueIds = await queryRunner.query(`
              SELECT DISTINCT id FROM venue WHERE deleted IS NOT TRUE
            `);
          }
          
          const venueIdList = venueIds.map(v => v.id);
          
          // Check for duplicates using the EXACT same logic as duplicate-detection.controller.ts
          const existingEventCheck = await queryRunner.query(`
            SELECT id, name, azure_id, "startDateTime", venue_id
            FROM event
            WHERE 
              deleted IS NOT TRUE
              AND name = $1
              AND venue_id = ANY($2::uuid[])
              AND "startDateTime" = $3
          `, [azureEvent.Name, venueIdList, startDateTime]);
          
          if (existingEventCheck.length > 0) {
            // Found existing event - update it with Azure ID instead of creating duplicate
            const existingEvent = existingEventCheck[0];
            this.logger.log(
              `Found existing event for Azure event ${azureEvent.Id} (${azureEvent.Name}): ${existingEvent.id} (${existingEvent.name})`
            );
            
            if (!existingEvent.azure_id) {
              // Update existing event with Azure ID
              await queryRunner.query(`
                UPDATE event
                SET 
                  azure_id = $1,
                  updated_at = NOW(),
                  id_validated = TRUE,
                  dedup_validated = TRUE, 
                  last_validation_date = NOW(),
                  validation_version = COALESCE(validation_version, 0) + 1
                WHERE id = $2
              `, [azureEvent.Id, existingEvent.id]);
              
              await queryRunner.commitTransaction();
              
              return {
                success: true,
                message: `Updated existing event with Azure ID: ${azureEvent.Name}`,
                postgresId: existingEvent.id,
                azureId: azureEvent.Id,
                venueName: venueName,
                venueId: venueId,
                action: 'updated_existing'
              };
            } else {
              // Event already has Azure ID - this shouldn't happen in normal flow
              await queryRunner.rollbackTransaction();
              this.logger.warn(`Event ${existingEvent.id} already has Azure ID ${existingEvent.azure_id}`);
              return {
                success: false,
                message: `Event already exists with Azure ID: ${azureEvent.Name}`,
                postgresId: existingEvent.id,
                azureId: existingEvent.azure_id,
                action: 'already_exists'
              };
            }
          }
          
          // No duplicates found - create new event
          const result = await queryRunner.manager.query(
            `INSERT INTO event (
              name, description, "startDateTime", "endDateTime", venue_id, 
              "imageUrl", azure_id, featured, created_at, updated_at, deleted,
              id_validated, dedup_validated, last_validation_date, validation_version
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
            RETURNING id`,
            [
              azureEvent.Name,
              azureEvent.Description || '',
              startDateTime,
              endDateTime,
              venueId,
              imageUrl,
              azureEvent.Id,
              azureEvent.FeatureLevel > 0 ? (azureEvent.FeatureLevel / 100) : 0.00, // Convert to numeric 0.00-1.00
              azureEvent.CreatedOn ? new Date(azureEvent.CreatedOn).toISOString() : new Date().toISOString(),
              azureEvent.UpdatedOn ? new Date(azureEvent.UpdatedOn).toISOString() : new Date().toISOString(),
              false,
              true,  // id_validated
              true,  // dedup_validated  
              new Date().toISOString(), // last_validation_date
              1      // validation_version
            ]
          );
          
          await queryRunner.commitTransaction();
          
          return {
            success: true,
            message: `Created new event in PostgreSQL: ${azureEvent.Name}`,
            postgresId: result[0].id,
            azureId: azureEvent.Id,
            venueName: venueName,
            venueId: venueId,
            action: 'created_new'
          };
        } catch (error) {
          await queryRunner.rollbackTransaction();
          this.logger.error(`Error creating event in PostgreSQL: ${error.message}`);
          throw new InternalServerErrorException(`Failed to create event in PostgreSQL: ${error.message}`);
        } finally {
          await queryRunner.release();
        }
      } finally {
        // Close the SQL connection
        if (pool) {
          await pool.close();
        }
      }
    } catch (error) {
      this.logger.error(`Error in createMissingEvent: ${error.message}`);
      throw new InternalServerErrorException(`Error creating missing event: ${error.message}`);
    }
  }
  
  @Post('resolve/:issueId')
  async resolveIssue(
    @Param('issueId') issueId: string,
    @Body() fixDto: FixIssueDto
  ) {
    // Update the issueId from the route parameter instead of the body
    fixDto.issueId = issueId;
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    
    try {
      // Get the issue details from the request
      const { issueId, resolution, matchId, azureId, mergeFields } = fixDto;
      this.logger.log(`Resolving issue ${issueId} with resolution: ${resolution}`);
      
      if (resolution === 'fix') {
        // Use provided Azure ID or generate a new one
        const finalAzureId = azureId || this.generateMockAzureId();
        
        // Check if the issueId is a UUID or a simple numeric ID
        const isUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(issueId);
        const isNumeric = /^\d+$/.test(issueId);
        
        if (!isUuid && !isNumeric) {
          throw new Error(`Invalid ID format: ${issueId}. Must be a UUID or numeric ID.`);
        }
        
        this.logger.log(`Processing ID: ${issueId} (format: ${isUuid ? 'UUID' : 'numeric'})`);

        // Try to determine the entity type by querying databases
        const venueCheck = await queryRunner.query(`SELECT id, name, google_place_id, address, city, state, website FROM venue WHERE id::text = $1 AND deleted IS NOT TRUE`, [issueId]);
        const musicianCheck = await queryRunner.query(`SELECT id, name, bio, category, genre, "imageUrl", email, "phoneNumber", website, "socialMedia" FROM talent WHERE id::text = $1`, [issueId]);
        const eventCheck = await queryRunner.query(`SELECT id FROM event WHERE id::text = $1 AND deleted IS NOT TRUE`, [issueId]);
        
        if (venueCheck.length > 0) {
            this.logger.log(`Fixing venue "${venueCheck[0].name}" with ${azureId ? 'provided' : 'new'} Azure ID: ${finalAzureId}`);
          
          // If we're using a manually provided Azure ID, we may want to merge data
          if (azureId && mergeFields) {
            try {
              // Fetch the actual Azure venue data including Locale field
              const azureVenue = await this.fetchAzureVenueForMerge(azureId);
              
              // Initialize data with defaults from PostgreSQL
              const azureData = {
                website: venueCheck[0].website,
                googlePlaceId: venueCheck[0].google_place_id,
                address: venueCheck[0].address,
                city: venueCheck[0].city,
                state: venueCheck[0].state,
                zipCode: venueCheck[0].zipCode,
                latitude: venueCheck[0].latitude,
                longitude: venueCheck[0].longitude
              };
              
              // If we have Azure data, use it to override nulls
              if (azureVenue) {
                // Get website from ContactWebsite
                if (!azureData.website && azureVenue.Website) {
                  azureData.website = azureVenue.Website;
                }
                
                // Get address data from structured Locale field
                if (!azureData.address && azureVenue.Address) {
                  azureData.address = azureVenue.Address;
                }
                
                if (!azureData.city && azureVenue.City) {
                  azureData.city = azureVenue.City;
                }
                
                if (!azureData.state && azureVenue.State) {
                  azureData.state = azureVenue.State;
                }
                
                if (!azureData.zipCode && azureVenue.PostalCode) {
                  azureData.zipCode = azureVenue.PostalCode;
                }
                
                // Add coordinates if available
                if ((!azureData.latitude || !azureData.longitude) && 
                    azureVenue.Latitude && azureVenue.Longitude) {
                  azureData.latitude = azureVenue.Latitude;
                  azureData.longitude = azureVenue.Longitude;
                }
              }
              
              this.logger.log(`Merging venue data with Azure record: ${JSON.stringify(azureData)}`);
              
              // Update with merged data - only use fields we know exist in PostgreSQL
              await queryRunner.query(`
                UPDATE venue 
                SET azure_id = $1,
                    website = COALESCE($2, website),
                    address = COALESCE($3, address),
                    city = COALESCE($4, city),
                    state = COALESCE($5, state),
                    "zipCode" = COALESCE($6, "zipCode"),
                    latitude = COALESCE($7, latitude),
                    longitude = COALESCE($8, longitude),
                    updated_at = NOW()
                WHERE id::text = $9
              `, [
                finalAzureId, 
                azureData.website,
                azureData.address,
                azureData.city,
                azureData.state,
                azureData.zipCode,
                azureData.latitude,
                azureData.longitude,
                issueId
              ]);
              
            } catch (error) {
              this.logger.error(`Error fetching Azure venue data: ${error.message}`);
              
              // If fetching Azure data fails, just update the Azure ID
              await queryRunner.query(`
                UPDATE venue 
                SET azure_id = $1,
                    updated_at = NOW()
                WHERE id::text = $2
              `, [finalAzureId, issueId]);
            }
          } else {
            // Simple update with just the Azure ID
            await queryRunner.query(`
              UPDATE venue 
              SET azure_id = $1,
                  updated_at = NOW()
              WHERE id::text = $2
            `, [finalAzureId, issueId]);
          }
        } 
        else if (musicianCheck.length > 0) {
          this.logger.log(`Fixing musician with ID ${issueId} with ${azureId ? 'provided' : 'new'} Azure ID: ${finalAzureId}`);
          
          // If we're using a manually provided Azure ID, we may want to merge data
          if (azureId && mergeFields) {
            try {
              // Fetch the actual Azure talent data
              const azureTalent = await this.fetchAzureTalentForMerge(azureId);
              
              // Initialize data with defaults from PostgreSQL
              const azureData = {
                name: musicianCheck[0].name,
                bio: musicianCheck[0].bio,
                category: musicianCheck[0].category,
                genre: musicianCheck[0].genre,
                imageUrl: musicianCheck[0].imageUrl,
                email: musicianCheck[0].email,
                phoneNumber: musicianCheck[0].phoneNumber,
                website: musicianCheck[0].website,
                socialMedia: musicianCheck[0].socialMedia
              };
              
              // If we have Azure data, use it to override nulls
              if (azureTalent) {
                this.logger.log(`Merging data from Azure talent ${azureTalent.Name} into PostgreSQL talent ${musicianCheck[0].name}`);
                
                // Get name if it exists in Azure
                if (azureTalent.Name) {
                  azureData.name = azureTalent.Name;
                }
                
                // Get bio if it exists in Azure
                if (azureTalent.Bio) {
                  azureData.bio = azureTalent.Bio;
                }
                
                // Get category if it exists in Azure
                if (azureTalent.Category) {
                  azureData.category = azureTalent.Category;
                }
                
                // Get genre if it exists in Azure
                if (azureTalent.Genre) {
                  azureData.genre = azureTalent.Genre;
                }
                
                // Get image URL if it exists in Azure
                if (azureTalent.ImageUrl) {
                  azureData.imageUrl = azureTalent.ImageUrl;
                }
                
                // Get email if it exists in Azure
                if (azureTalent.Email) {
                  azureData.email = azureTalent.Email;
                }
                
                // Get phone number if it exists in Azure
                if (azureTalent.PhoneNumber) {
                  azureData.phoneNumber = azureTalent.PhoneNumber;
                }
                
                // Get website if it exists in Azure
                if (azureTalent.Website) {
                  azureData.website = azureTalent.Website;
                }
                
                // Get social media if it exists in Azure
                if (azureTalent.SocialMedia) {
                  azureData.socialMedia = typeof azureTalent.SocialMedia === 'string' 
                    ? azureTalent.SocialMedia 
                    : JSON.stringify(azureTalent.SocialMedia);
                }
              }
              
              this.logger.log(`Merging talent data with Azure record: ${JSON.stringify(azureData)}`);
              
              // Update with merged data - only use fields we know exist in PostgreSQL
              await queryRunner.query(`
                UPDATE talent 
                SET azure_id = $1,
                    name = COALESCE($2, name),
                    bio = COALESCE($3, bio),
                    category = COALESCE($4, category),
                    genre = COALESCE($5, genre),
                    "imageUrl" = COALESCE($6, "imageUrl"),
                    email = COALESCE($7, email),
                    "phoneNumber" = COALESCE($8, "phoneNumber"),
                    website = COALESCE($9, website),
                    "socialMedia" = COALESCE($10, "socialMedia"),
                    "updatedAt" = NOW()
                WHERE id::text = $11
              `, [
                finalAzureId, 
                azureData.name,
                azureData.bio,
                azureData.category,
                azureData.genre,
                azureData.imageUrl,
                azureData.email,
                azureData.phoneNumber,
                azureData.website,
                azureData.socialMedia,
                issueId
              ]);
            } catch (error) {
              this.logger.error(`Error fetching Azure talent data: ${error.message}`);
              
              // If fetching Azure data fails, just update the Azure ID
              await queryRunner.query(`
                UPDATE talent 
                SET azure_id = $1,
                    "updatedAt" = NOW()
                WHERE id::text = $2
              `, [finalAzureId, issueId]);
            }
          } else {
            // Simple update with just the Azure ID
            await queryRunner.query(`
              UPDATE talent 
              SET azure_id = $1,
                  "updatedAt" = NOW()
              WHERE id::text = $2
            `, [finalAzureId, issueId]);
          }
        }
        else if (eventCheck.length > 0) {
          this.logger.log(`Fixing event with ID ${issueId} with ${azureId ? 'provided' : 'new'} Azure ID: ${finalAzureId}`);
          
          // If we're using a manually provided Azure ID, we may want to merge data
          if (azureId && mergeFields) {
            try {
              // Fetch the actual Azure event data
              const azureEvent = await this.fetchAzureEventForMerge(azureId);
              
              // Get the full event record from PostgreSQL to use as defaults
              const fullEventCheck = await queryRunner.query(`
                SELECT e.id, e.name, e.description, e."startDateTime", e."endDateTime", 
                       e.venue_id, e."imageUrl", e.media, e.featured,
                       e.status
                FROM event e WHERE e.id::text = $1 AND e.deleted IS NOT TRUE
              `, [issueId]);
              
              if (fullEventCheck.length === 0) {
                throw new Error(`Event with ID ${issueId} not found in PostgreSQL`);
              }
              
              // Initialize data with defaults from PostgreSQL
              const pgEvent = fullEventCheck[0];
              const azureData = {
                name: pgEvent.name,
                description: pgEvent.description,
                startDateTime: pgEvent.startDateTime,
                endDateTime: pgEvent.endDateTime,
                venue_id: pgEvent.venue_id,
                imageUrl: pgEvent.imageUrl,
                media: pgEvent.media,
                featured: pgEvent.featured,

              };
              
              // If we have Azure data, use it to override nulls
              if (azureEvent) {
                this.logger.log(`Merging data from Azure event ${azureEvent.Name}`);
                
                // Get name if it exists in Azure
                if (azureEvent.Name) {
                  azureData.name = azureEvent.Name;
                }
                
                // Get description if it exists in Azure
                if (azureEvent.Description) {
                  azureData.description = azureEvent.Description;
                }
                
                // Get date/time values if they exist in Azure
                // Use string versions of dates to avoid timezone conversion issues
                if (azureEvent.StartDateTimeStr) {
                  // Use the string version directly for SQL updates
                  azureData.startDateTime = azureEvent.StartDateTimeStr;
                  this.logger.log(`Using Azure start date/time: ${azureEvent.StartDateTimeStr}`);
                } else if (azureEvent.StartDateTime) {
                  // Fallback to ISO format if string version not available
                  azureData.startDateTime = azureEvent.StartDateTime;
                  this.logger.log(`Using Azure start date/time (ISO): ${azureEvent.StartDateTime}`);
                }
                
                if (azureEvent.EndDateTimeStr) {
                  // Use the string version directly for SQL updates
                  azureData.endDateTime = azureEvent.EndDateTimeStr;
                  this.logger.log(`Using Azure end date/time: ${azureEvent.EndDateTimeStr}`);
                } else if (azureEvent.EndDateTime) {
                  // Fallback to ISO format if string version not available
                  azureData.endDateTime = azureEvent.EndDateTime;
                  this.logger.log(`Using Azure end date/time (ISO): ${azureEvent.EndDateTime}`);
                }
                
                // Get image URL if it exists in Azure
                if (azureEvent.ImageUrl) {
                  azureData.imageUrl = azureEvent.ImageUrl;
                }
                
                // Get media JSON if it exists in Azure
                if (azureEvent.Media) {
                  // If PostgreSQL already has media, merge the objects
                  if (azureData.media) {
                    try {
                      const existingMedia = typeof azureData.media === 'string' 
                        ? JSON.parse(azureData.media) 
                        : azureData.media;
                      
                      const azureMedia = typeof azureEvent.Media === 'string'
                        ? JSON.parse(azureEvent.Media)
                        : azureEvent.Media;
                      
                      azureData.media = JSON.stringify({...existingMedia, ...azureMedia});
                    } catch (e) {
                      this.logger.error(`Error merging media JSON: ${e.message}`);
                      azureData.media = azureEvent.Media;
                    }
                  } else {
                    azureData.media = azureEvent.Media;
                  }
                }
                
                // Set featured status if specified in Azure
                if (azureEvent.Featured !== undefined) {
                  azureData.featured = azureEvent.Featured;
                }
                

              }
              
              this.logger.log(`Merging event data with Azure record: ${JSON.stringify(azureData)}`);
              
              // Update with merged data - only use fields we know exist in PostgreSQL
              // Also mark as validated to prevent cyclical validation
              await queryRunner.query(`
                UPDATE event 
                SET azure_id = $1,
                    name = COALESCE($2, name),
                    description = COALESCE($3, description),
                    "startDateTime" = COALESCE($4, "startDateTime"),
                    "endDateTime" = COALESCE($5, "endDateTime"),
                    "imageUrl" = COALESCE($6, "imageUrl"),
                    media = COALESCE($7, media),
                    featured = COALESCE($8, featured),

                    updated_at = NOW(),
                    id_validated = TRUE,
                    dedup_validated = TRUE,
                    last_validation_date = NOW(),
                    validation_version = validation_version + 1
                WHERE id::text = $9
              `, [
                finalAzureId,
                azureData.name,
                azureData.description,
                azureData.startDateTime,
                azureData.endDateTime,
                azureData.imageUrl,
                azureData.media,
                azureData.featured,
                issueId
              ]);
              
              this.logger.log(`Event ${issueId} has been marked as fully validated to prevent cyclical validation`);
            } catch (error) {
              this.logger.error(`Error fetching Azure event data: ${error.message}`);
              
              // If fetching Azure data fails, just update the Azure ID
              // Still mark as validated to prevent cyclical validation
              await queryRunner.query(`
                UPDATE event 
                SET azure_id = $1,
                    updated_at = NOW(),
                    id_validated = TRUE,
                    dedup_validated = TRUE,
                    last_validation_date = NOW(),
                    validation_version = validation_version + 1
                WHERE id::text = $2
              `, [finalAzureId, issueId]);
              
              this.logger.log(`Event ${issueId} has been marked as fully validated (with Azure ID only) to prevent cyclical validation`);
            }
          } else {
            // Simple update with just the Azure ID
            // Also mark as validated to prevent cyclical validation
            await queryRunner.query(`
              UPDATE event 
              SET azure_id = $1,
                  updated_at = NOW(),
                  id_validated = TRUE,
                  dedup_validated = TRUE,
                  last_validation_date = NOW(),
                  validation_version = validation_version + 1
              WHERE id::text = $2
            `, [finalAzureId, issueId]);
            
            this.logger.log(`Event ${issueId} has been marked as fully validated with Azure ID: ${finalAzureId}`);
          }
        }
        else {
          throw new Error(`Entity with ID ${issueId} not found`);
        }
      } 
      else if (resolution === 'merge') {
        // For "merge" resolution, we'd merge records between PostgreSQL and Azure
        if (!matchId) {
          throw new Error('Match ID is required for merge operations');
        }
        
        // First check if this is a valid UUID for the matchId
        const isValidMatchUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(matchId);
        if (!isValidMatchUuid) {
          throw new Error(`Invalid UUID format for matchId: ${matchId}`);
        }
        
        const venueCheck = await queryRunner.query(`SELECT id, name FROM venue WHERE id::text = $1`, [issueId]);
        const musicianCheck = await queryRunner.query(`SELECT id, name, bio, category, genre, "imageUrl", email, "phoneNumber", website, "socialMedia" FROM talent WHERE id::text = $1`, [issueId]);
        const eventCheck = await queryRunner.query(`SELECT id FROM event WHERE id::text = $1 AND deleted IS NOT TRUE`, [issueId]);
        
        if (venueCheck.length > 0) {
          this.logger.log(`Merging venue "${venueCheck[0].name}" with Azure ID: ${matchId}`);
          
          // First, get the target record we're merging with
          const targetVenue = await queryRunner.query(`SELECT id, name FROM venue WHERE azure_id::text = $1`, [matchId]);
          
          if (targetVenue.length > 0) {
            this.logger.log(`Found target venue "${targetVenue[0].name}" for merge`);
            
            // Option 1: Update references to the duplicate venue in other tables
            // For example, update event.venue_id to point to the target venue
            await queryRunner.query(`
              UPDATE event 
              SET venue_id = $1,
                  updated_at = NOW()
              WHERE venue_id::text = $2
            `, [targetVenue[0].id, issueId]);
            
            // Before marking as deleted, let's try to extract any useful address data
            // Get the full venue data from Azure using the matchId
            const azureVenue = await this.fetchAzureVenueForMerge(matchId);
            
            if (azureVenue && azureVenue.Locale) {
              try {
                // Parse the Locale object which contains address data
                let address = '';
                let city = '';
                let state = '';
                let zipCode = '';
                let latitude = null;
                let longitude = null;
                let formatted_address = '';
                
                // Extract the nested address data if it exists
                if (azureVenue.Locale.MapLocation && azureVenue.Locale.MapLocation.address) {
                  const addressData = azureVenue.Locale.MapLocation.address;
                  
                  // Combine street number and street for address
                  if (addressData.streetNumber && addressData.street) {
                    address = `${addressData.streetNumber} ${addressData.street}`;
                  }
                  
                  // Get city, state, and postal code
                  city = addressData.city || '';
                  state = addressData.state || '';
                  zipCode = addressData.postalcode || '';
                  
                  // Use full_address as formatted_address
                  formatted_address = addressData.full_address || '';
                  
                  // Get coordinates if available
                  if (addressData.coordinates) {
                    latitude = addressData.coordinates.lat;
                    longitude = addressData.coordinates.lng;
                  }
                  
                  // Update the target venue with this address data if any fields are empty
                  const updateFields = [];
                  const updateValues = [];
                  let paramIndex = 1;
                  
                  if (address && address.trim() !== '') {
                    updateFields.push(`address = $${paramIndex}`);
                    updateValues.push(address);
                    paramIndex++;
                  }
                  
                  if (city && city.trim() !== '') {
                    updateFields.push(`city = $${paramIndex}`);
                    updateValues.push(city);
                    paramIndex++;
                  }
                  
                  if (state && state.trim() !== '') {
                    updateFields.push(`state = $${paramIndex}`);
                    updateValues.push(state);
                    paramIndex++;
                  }
                  
                  if (zipCode && zipCode.trim() !== '') {
                    updateFields.push(`"zipCode" = $${paramIndex}`);
                    updateValues.push(zipCode);
                    paramIndex++;
                  }
                  
                  if (formatted_address && formatted_address.trim() !== '') {
                    updateFields.push(`formatted_address = $${paramIndex}`);
                    updateValues.push(formatted_address);
                    paramIndex++;
                  }
                  
                  if (latitude !== null) {
                    updateFields.push(`latitude = $${paramIndex}`);
                    updateValues.push(latitude);
                    paramIndex++;
                  }
                  
                  if (longitude !== null) {
                    updateFields.push(`longitude = $${paramIndex}`);
                    updateValues.push(longitude);
                    paramIndex++;
                  }
                  
                  // Only update if we have fields to update
                  if (updateFields.length > 0) {
                    // Add the target venue ID as the last parameter
                    updateValues.push(targetVenue[0].id);
                    
                    await queryRunner.query(`
                      UPDATE venue 
                      SET ${updateFields.join(', ')},
                          updated_at = NOW()
                      WHERE id = $${paramIndex}
                    `, updateValues);
                    
                    this.logger.log(`Updated target venue with address data from Azure`);
                  }
                }
              } catch (error) {
                this.logger.error(`Error extracting address data: ${error.message}`);
              }
            }
            
            // Now mark the duplicate as deleted
            await queryRunner.query(`
              UPDATE venue 
              SET is_deleted = true,
                  azure_id = $1,
                  updated_at = NOW()
              WHERE id::text = $2
            `, [matchId, issueId]);
            
            this.logger.log(`Marked duplicate venue as deleted and updated references`);
          } else {
            // If no target exists, just update the Azure ID
            await queryRunner.query(`
              UPDATE venue 
              SET azure_id = $1,
                  updated_at = NOW()
              WHERE id::text = $2
            `, [matchId, issueId]);
          }
        } 
        else if (musicianCheck.length > 0) {
          // Find target musician by Azure ID
          const targetMusician = await queryRunner.query(`SELECT id, name FROM talent WHERE azure_id::text = $1`, [matchId]);
          
          if (targetMusician.length > 0) {
            // Update references in events_talents junction table
            await queryRunner.query(`
              UPDATE events_talents 
              SET talent_id = $1
              WHERE talent_id::text = $2
            `, [targetMusician[0].id, issueId]);
            
            // Mark the duplicate as deleted
            await queryRunner.query(`
              UPDATE talent 
              SET is_deleted = true,
                  azure_id = $1,
                  updated_at = NOW()
              WHERE id::text = $2
            `, [matchId, issueId]);
          } else {
            // Just update the Azure ID
            await queryRunner.query(`
              UPDATE talent 
              SET azure_id = $1,
                  "updatedAt" = NOW()
              WHERE id::text = $2
            `, [matchId, issueId]);
          }
        }
        else if (eventCheck.length > 0) {
          // Find target event by Azure ID
          const targetEvent = await queryRunner.query(`SELECT id, name FROM event WHERE azure_id::text = $1 AND deleted IS NOT TRUE`, [matchId]);
          
          if (targetEvent.length > 0) {
            // Update references in events_talents junction table
            await queryRunner.query(`
              UPDATE events_talents 
              SET event_id = $1
              WHERE event_id::text = $2
            `, [targetEvent[0].id, issueId]);
            
            // Mark the duplicate as deleted
            await queryRunner.query(`
              UPDATE event 
              SET is_deleted = true,
                  azure_id = $1,
                  updated_at = NOW()
              WHERE id::text = $2
            `, [matchId, issueId]);
          } else {
            // Just update the Azure ID
            await queryRunner.query(`
              UPDATE event 
              SET azure_id = $1,
                  updated_at = NOW()
              WHERE id::text = $2
            `, [matchId, issueId]);
          }
        }
        else {
          throw new Error(`Entity with ID ${issueId} not found`);
        }
      }
      
      await queryRunner.commitTransaction();
      return { success: true, message: 'Issue resolved successfully' };
      
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Error resolving issue: ${error.message}`);
      throw new InternalServerErrorException(`Failed to resolve issue: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }
  
  // Helper function to generate a realistic looking GUID for Azure ID
  private generateMockAzureId(): string {
    const s4 = () => Math.floor((1 + Math.random()) * 0x10000).toString(16).substring(1);
    return `${s4()}${s4()}-${s4()}-${s4()}-${s4()}-${s4()}${s4()}${s4()}`;
  }

  // Helper function to sanitize strings for SQL queries (prevent SQL injection)
  private sanitizeForSql(str: string): string {
    if (!str) return '';
    return str.replace(/'/g, "''");
  }
  
  /**
   * Get events with ID validation issues:
   * 1. Events that exist in MSSQL but not in PostgreSQL
   * 2. Events in PostgreSQL that are missing Azure IDs
   */
  private async getMissingEvents(): Promise<ValidationIssue[]> {
    this.logger.log('Fetching all event ID validation issues');
    
    try {
      // Connect to Azure SQL
      const sql = require('mssql');
      const pool = await this.checkAzureDbConnection();
      
      try {
        // Initialize the issues array
        const issues: ValidationIssue[] = [];
        
        // PART 1: Find events in MSSQL that don't exist in PostgreSQL
        this.logger.log('Finding events in MSSQL that don\'t exist in PostgreSQL');
        
        // Get ALL events from MSSQL without date filtering or limit
        // Use CONVERT to get string representation and avoid timezone conversion
        // Include both date and time fields since they're separate in MSSQL
        const allEventsQuery = `
          SELECT 
            Id, 
            Name, 
            EventStartDate,
            EventStartTime,
            CONVERT(varchar, EventStartDate, 23) AS EventStartDateStr,
            CONVERT(varchar, EventStartTime, 8) AS EventStartTimeStr,
            VenueId, 
            Description
          FROM PerformanceEvents
        `;
        
        // Execute query to get all events from MSSQL
        const allEvents = await pool.request().query(allEventsQuery);
        this.logger.log(`Found ${allEvents.recordset.length} total events in MSSQL`);
        
        // Get all events from PostgreSQL
        // Filter out deleted records and only include events that haven't been validated yet
        const pgEventsQuery = `
          SELECT e.id, e.azure_id, e.name, e.description, e."startDateTime", e.venue_id, e.id_validated,
                 v.name as venue_name
          FROM event e
          LEFT JOIN venue v ON e.venue_id = v.id
          WHERE e.deleted IS NOT TRUE
        `;
        const pgEvents = await this.dataSource.query(pgEventsQuery);
        
        // Create a set of Azure IDs from PostgreSQL events for efficient lookup
        const pgAzureIds = new Set();
        const pgEventsWithoutAzureId = [];
        for (const pgEvent of pgEvents) {
          if (pgEvent.azure_id) {
            pgAzureIds.add(pgEvent.azure_id.toUpperCase());
          } else if (pgEvent.id_validated !== true) {
            // This event is missing an Azure ID and hasn't been validated yet
            pgEventsWithoutAzureId.push(pgEvent);
          }
        }
        
        this.logger.log(`Found ${pgEventsWithoutAzureId.length} events in PostgreSQL missing Azure IDs`);
        
        // Find all events in MSSQL that don't exist in PostgreSQL
        const missingEvents = [];
        
        for (const event of allEvents.recordset) {
          const azureEventIdUpper = event.Id.toUpperCase();
          if (!pgAzureIds.has(azureEventIdUpper)) {
            missingEvents.push(event);
          }
        }
        
        this.logger.log(`Found ${missingEvents.length} events in MSSQL that don't exist in PostgreSQL`);
        
        // Get ALL venue information from MSSQL for better lookup
        this.logger.log('Fetching all venue information from MSSQL...');
        const venueMap = new Map();
        try {
          // Get all venues from MSSQL
          const allVenuesQuery = `SELECT Id, Name FROM Venues`;
          const allVenues = await pool.request().query(allVenuesQuery);
          
          this.logger.log(`Found ${allVenues.recordset.length} venues in MSSQL`);
          
          // Create a map of venue IDs to venue names
          for (const venue of allVenues.recordset) {
            if (venue.Id && venue.Name) {
              venueMap.set(venue.Id.toUpperCase(), venue.Name);
            }
          }
        } catch (venueError) {
          this.logger.error(`Error fetching venue information: ${venueError.message}`);
        }
        
        // Create validation issues for events missing in PostgreSQL
        this.logger.log('Creating validation issues for events missing in PostgreSQL...');
        
        // Sort the missing events by date (most recent first) so we show the most relevant ones
        // Use the string representation to avoid timezone conversion issues
        missingEvents.sort((a, b) => {
          const dateA = a.EventStartDateStr || '0000-00-00';
          const dateB = b.EventStartDateStr || '0000-00-00';
          return dateB.localeCompare(dateA); // String comparison works for YYYY-MM-DD format
        });
        
        // We'll show all missing events now, not just 44
        for (const event of missingEvents) {
          // Format date for display using the string representation
          const formattedDate = event.EventStartDateStr || 'Unknown';
          
          // Get venue name if available - properly handle case sensitivity
          const venueName = event.VenueId ? 
            (venueMap.get(event.VenueId.toUpperCase()) || 'Unknown Venue') : 'Unknown Venue';
          
          // Log if we couldn't find the venue
          if (event.VenueId && !venueMap.has(event.VenueId.toUpperCase())) {
            this.logger.warn(`Could not find venue with ID ${event.VenueId} for event ${event.Name}`);
          }
          
          // Create a simplified record - use venue_name to match frontend expectations
          // Combine separate MSSQL date and time fields into unified PostgreSQL format
          const startDateTimeStr = event.EventStartDateStr && event.EventStartTimeStr ? 
            `${event.EventStartDateStr} ${event.EventStartTimeStr}` : 
            (event.EventStartDateStr ? `${event.EventStartDateStr} 00:00:00` : null);
          
          const azureRecord = {
            id: event.Id,
            name: event.Name || 'Unknown Event',
            description: event.Description || '',
            startDateTime: startDateTimeStr,
            venue_name: venueName
          };
          
          issues.push({
            id: this.generateMockAzureId(),
            entityType: 'events',
            postgresRecord: null,
            azureRecord: azureRecord,
            issueType: 'missing_postgres',
            description: `Event "${event.Name || 'Unknown Event'}" exists in MSSQL but not in PostgreSQL. Event date: ${formattedDate}`,
            createdAt: new Date().toISOString()
          });
        }
        
        // PART 2: Find events in PostgreSQL that are missing Azure IDs
        this.logger.log('Creating validation issues for events missing Azure IDs...');
        
        // Get ALL venue information from PostgreSQL for better lookup
        this.logger.log('Fetching all venue information from PostgreSQL...');
        const pgVenueMap = new Map();
        try {
          // Get all venues from PostgreSQL
          const allVenuesQuery = 'SELECT id, name FROM venue WHERE deleted = FALSE';
          const allVenues = await this.dataSource.query(allVenuesQuery);
          
          this.logger.log(`Found ${allVenues.length} venues in PostgreSQL`);
          
          // Create a map of venue IDs to venue names
          for (const venue of allVenues) {
            if (venue.id && venue.name) {
              pgVenueMap.set(venue.id, venue.name);
            }
          }
        } catch (venueError) {
          this.logger.error(`Error fetching PostgreSQL venue information: ${venueError.message}`);
        }
        
        // Add issues for PostgreSQL events missing Azure IDs (only for those not already validated)
        for (const event of pgEventsWithoutAzureId) {
          // Format date for display - fix timezone issue by using string manipulation instead of Date()
          const formattedDate = event.startDateTime ? 
            (event.startDateTime.includes('T') ? 
              event.startDateTime.split('T')[0] : 
              event.startDateTime.split(' ')[0]) : 'Unknown';
          
          let venueName = 'Unknown Venue';
          
          // If we have an Azure ID but no venue name, try to get venue info from Azure MSSQL
          if (event.azure_id && !event.venue_name) {
            try {
              const azureEventQuery = `
                SELECT e.Name, e.VenueId, v.Name as VenueName
                FROM PerformanceEvents e
                LEFT JOIN Venues v ON e.VenueId = v.Id  
                WHERE e.Id = '${this.sanitizeForSql(event.azure_id)}'
              `;
              
              const azureResult = await pool.request().query(azureEventQuery);
              if (azureResult.recordset && azureResult.recordset.length > 0) {
                const azureEvent = azureResult.recordset[0];
                venueName = azureEvent.VenueName || 'Unknown Venue';
                this.logger.log(`Found venue from Azure for event ${event.name}: ${venueName}`);
              }
            } catch (error) {
              this.logger.warn(`Could not query Azure for venue info for event ${event.name}: ${error.message}`);
            }
          } else if (event.venue_name) {
            // Use PostgreSQL venue name if available
            venueName = event.venue_name;
          }
          
          // Log if we still couldn't find venue info
          if (venueName === 'Unknown Venue') {
            this.logger.warn(`Event ${event.name} has no venue information available in PostgreSQL or Azure`);
          }
          
          // Create a simplified record - use venue_name to match frontend expectations
          const pgRecord = {
            id: event.id,
            name: event.name || 'Unknown Event',
            description: event.description || '',
            startDateTime: event.startDateTime,
            venue_name: venueName
          };
          
          issues.push({
            id: this.generateMockAzureId(),
            entityType: 'events',
            postgresRecord: pgRecord,
            azureRecord: null,
            issueType: 'missing_azure',
            description: `Event "${event.name || 'Unknown Event'}" exists in PostgreSQL but is missing an Azure ID. Event date: ${formattedDate}`,
            createdAt: new Date().toISOString()
          });
        }
        
        this.logger.log(`Created ${issues.length} validation issues for missing events`);
        
        this.logger.log(`Found ${issues.length} events in MSSQL that are missing from PostgreSQL`);
        
        return issues;
      } finally {
        // Always close the pool when done
        if (pool) {
          try {
            await pool.close();
            this.logger.log('MSSQL connection closed');
          } catch (closeError) {
            this.logger.error(`Error closing MSSQL connection: ${closeError.message}`);
          }
        }
      }
    } catch (error) {
      this.logger.error(`Error fetching missing events: ${error.message}`);
      throw new InternalServerErrorException('Failed to fetch missing events');
    }
  }
  
  // Helper to fetch Azure talent/musician data with full details for merging
  private async fetchAzureTalentForMerge(azureId: string) {
    const sql = require('mssql');
    let pool = null;
    
    try {
      // Azure connection config
      const config = {
        server: 'mssql.drv1.umbhost.net',
        database: 'TLM',
        user: 'Reader',
        password: 'TLM1234!',
        options: {
          encrypt: false,
          trustServerCertificate: true
        }
      };
      
      // Create connection pool
      pool = await sql.connect(config);
      
      this.logger.log(`Successfully connected to Azure SQL`);
      
      // Get talent data with all fields including ProfileImageCropperValue
      const result = await pool.request()
        .input('talentId', azureId)
        .query(`
          SELECT 
            Id, 
            Name,
            Description as Bio,
            FeatureLevel as Category,
            ContactName,
            ContactEmailAddress as Email,
            ContactPhoneNumber as PhoneNumber,
            ContactWebsite as Website,
            ProfileImageCropperValue,
            ProfileImageId,
            Url,
            CreatedOn as CreatedAt,
            UpdatedOn as UpdatedAt
          FROM Talent 
          WHERE Id = @talentId
        `);
      
      if (result.recordset.length === 0) {
        this.logger.error(`Talent with Azure ID ${azureId} not found`);
        return null;
      }
      
      const talent = result.recordset[0];
      
      // Process image data
      if (talent.ProfileImageCropperValue && typeof talent.ProfileImageCropperValue === 'string') {
        try {
          const imageData = JSON.parse(talent.ProfileImageCropperValue);
          this.logger.log('Parsed ProfileImageCropperValue successfully');
          
          // Extract image URL from Src property
          if (imageData && imageData.Src) {
            // Create full URL by adding base domain if it's a relative path
            const imagePath = imageData.Src;
            // Use this for debugging image paths
            this.logger.log(`Found image path: ${imagePath}`);
            
            // Store the original image path from Umbraco
            talent.ImageUrl = imagePath.startsWith('http') 
              ? imagePath 
              : `https://tucsonlovesmusic.com${imagePath}`;
            
            // Also store the raw image data for frontend reference
            talent.ImageData = imageData;
          }
        } catch (e) {
          this.logger.error(`Error parsing talent ProfileImageCropperValue JSON: ${e.message}`);
        }
      }
      
      // Parse SocialMedia JSON if it exists
      if (talent.SocialMedia && typeof talent.SocialMedia === 'string') {
        try {
          talent.SocialMedia = JSON.parse(talent.SocialMedia);
          this.logger.log('Parsed SocialMedia successfully');
        } catch (e) {
          this.logger.error(`Error parsing talent SocialMedia JSON: ${e.message}`);
        }
      }
      
      return talent;
    } catch (error) {
      this.logger.error(`Error fetching Azure talent data: ${error.message}`);
      return null;
    } finally {
      if (pool) {
        // Close the connection
        await pool.close();
      }
    }
  }
  
  // Helper to fetch Azure venue data with full details for merging
  private async fetchAzureVenueForMerge(azureId: string) {
    const sql = require('mssql');
    let pool = null;
    
    try {
      // Azure connection config
      const config = {
        server: 'mssql.drv1.umbhost.net',
        database: 'TLM',
        user: 'Reader',
        password: 'TLM1234!',
        options: {
          encrypt: false,
          trustServerCertificate: true
        }
      };
      
      // Create connection pool
      pool = await sql.connect(config);
      
      this.logger.log(`Successfully connected to Azure SQL`);
      
      // Get venue data with all fields including Locale
      // Only select columns that actually exist in the Azure MSSQL table
      const result = await pool.request()
        .input('venueId', azureId)
        .query(`
          SELECT 
            Id, 
            Name, 
            Locale,
            ContactWebsite as Website, 
            CreatedOn as CreatedAt,
            UpdatedOn as UpdatedAt
          FROM Venues 
          WHERE Id = @venueId
        `);
      
      if (result.recordset.length === 0) {
        this.logger.error(`Venue with Azure ID ${azureId} not found`);
        return null;
      }
      
      const venue = result.recordset[0];
      
      // Parse Locale JSON if it exists
      if (venue.Locale && typeof venue.Locale === 'string') {
        try {
          venue.Locale = JSON.parse(venue.Locale);
          this.logger.log('Parsed Locale successfully', venue.Locale);
          
          // Extract address data from Locale.MapLocation.address if available
          if (venue.Locale.MapLocation?.address) {
            const { address } = venue.Locale.MapLocation;
            
            // Add all address components to venue object
            venue.Address = address.full_address || null;
            venue.City = address.city || null;
            venue.State = address.state || null;
            venue.PostalCode = address.postalcode || null;
            
            // Extract street components if available
            if (address.streetNumber && address.street) {
              venue.StreetNumber = address.streetNumber;
              venue.Street = address.street;
              // Create a clean street address (just number + street)
              venue.StreetAddress = `${address.streetNumber} ${address.street}`;
            }
            
            // Log successful address extraction
            if (address.full_address) {
              this.logger.log(`Found address in Locale: ${address.full_address}`);
            }
            
            // Extract coordinates - try multiple possible paths
            if (venue.Locale.MapLocation.lat && venue.Locale.MapLocation.lng) {
              venue.Latitude = venue.Locale.MapLocation.lat;
              venue.Longitude = venue.Locale.MapLocation.lng;
            } else if (address.coordinates && !address.coordinates.IsEmpty) {
              venue.Latitude = address.coordinates.lat;
              venue.Longitude = address.coordinates.lng;
            } else if (venue.Locale.lat && venue.Locale.lng) {
              venue.Latitude = venue.Locale.lat;
              venue.Longitude = venue.Locale.lng;
            }
          }
        } catch (e) {
          this.logger.error(`Error parsing venue Locale JSON: ${e.message}`);
        }
      }
      
      return venue;
    } catch (error) {
      this.logger.error(`Error fetching Azure venue data: ${error.message}`);
      return null;
    } finally {
      if (pool) {
        // Close the connection
        await pool.close();
      }
    }
  }
  
  // Helper to fetch Azure event data with full details for merging
  /**
   * Soft delete a record with validation issues
   * This endpoint allows administrators to soft delete entities that have validation issues
   * Following our established soft delete pattern, entities are marked as deleted but remain in the database
   * @param entityType - The type of entity (venues, musicians, events)
   * @param id - The ID of the entity in PostgreSQL
   */
  @Delete(':entityType/:id')
  async deleteRecord(
    @Param('entityType') entityType: string,
    @Param('id') id: string
  ) {
    this.logger.log(`Attempting to soft delete ${entityType} record with ID: ${id}`);
    
    // Validate the entity type
    if (!['venues', 'musicians', 'events'].includes(entityType)) {
      throw new BadRequestException(`Invalid entity type: ${entityType}`);
    }
    
    try {
      // Map entity types to their correct table names using TypeORM's default naming strategy
      const entityTableMap = {
        'venues': 'venue',
        'musicians': 'talent', // Musicians are stored in the 'talent' table
        'events': 'event'
      };
      
      const tableName = entityTableMap[entityType];
      
      if (!tableName) {
        throw new BadRequestException(`Unknown entity type: ${entityType}`);
      }
      
      // Verify the record exists before attempting to soft delete it
      const checkQuery = `SELECT id FROM ${tableName} WHERE id = $1 AND deleted = false`;
      const checkResult = await this.dataSource.query(checkQuery, [id]);
      
      if (!checkResult || checkResult.length === 0) {
        throw new NotFoundException(`${entityType} record with ID ${id} not found or already deleted`);
      }
      
      // Update the record to set deleted flag to true and update the timestamp
      const updateQuery = `
        UPDATE ${tableName} 
        SET deleted = true, 
            updated_at = NOW() 
        WHERE id = $1 
        RETURNING id
      `;
      
      const result = await this.dataSource.query(updateQuery, [id]);
      
      if (!result || result.length === 0) {
        throw new InternalServerErrorException(`Failed to soft delete ${entityType} record with ID ${id}`);
      }
      
      this.logger.log(`Successfully soft deleted ${entityType} record with ID: ${id}`);
      
      return {
        success: true,
        message: `${entityType.slice(0, -1)} has been moved to the Deleted Items section`,
        id: id
      };
    } catch (error) {
      this.logger.error(`Error soft deleting ${entityType} record: ${error.message}`, error.stack);
      
      // Enhanced error handling based on error type
      if (error instanceof NotFoundException) {
        throw error;
      } else if (error.code === '23503') { // Foreign key constraint violation
        throw new BadRequestException(
          `Cannot delete this ${entityType.slice(0, -1)} because it is referenced by other records. ` +
          `Please remove any related records first.`
        );
      } else if (error instanceof BadRequestException || error instanceof InternalServerErrorException) {
        throw error;
      } else {
        throw new InternalServerErrorException(
          `Failed to delete ${entityType.slice(0, -1)}: ${error.message}`
        );
      }
    }
  }
  
  private async fetchAzureEventForMerge(azureId: string) {
    const sql = require('mssql');
    let pool = null;
    
    try {
      // Azure connection config
      const config = {
        server: 'mssql.drv1.umbhost.net',
        database: 'TLM',
        user: 'Reader',
        password: 'TLM1234!',
        options: {
          encrypt: false,
          trustServerCertificate: true
        }
      };
      
      // Create connection pool
      pool = await sql.connect(config);
      
      this.logger.log(`Successfully connected to Azure SQL for event fetch`);
      
      // Query event data using the Azure ID with all relevant fields
      const result = await pool.request()
        .input('eventId', sql.UniqueIdentifier, azureId)
        .query(`
          SELECT 
            Id, 
            Name, 
            Description,
            EventStartDate,
            EventStartTime,
            EventEndDate,
            EventEndTime,
            VenueId,
            FeatureLevel,
            ProfileImageCropperValue,
            ContactName,
            ContactEmailAddress,
            ContactPhoneNumber,
            ContactWebsite,
            Locale,
            Url,
            CreatedOn,
            UpdatedOn
          FROM PerformanceEvents 
          WHERE Id = @eventId
        `);
      
      if (result.recordset.length === 0) {
        this.logger.error(`Event with Azure ID ${azureId} not found`);
        return null;
      }
      
      const azureEvent = result.recordset[0];
      
      // Log the raw event data for debugging time fields
      this.logger.log(`Raw Azure event data from SQL:`);
      this.logger.log(`EventStartDate: ${JSON.stringify(azureEvent.EventStartDate)}`);
      this.logger.log(`EventStartTime: ${JSON.stringify(azureEvent.EventStartTime)}`);
      this.logger.log(`EventEndDate: ${JSON.stringify(azureEvent.EventEndDate)}`);
      this.logger.log(`EventEndTime: ${JSON.stringify(azureEvent.EventEndTime)}`);
      
      // Combine date and time fields to create proper DateTime strings using string manipulation
      // to avoid timezone conversion issues
      let startDateTime = null;
      let endDateTime = null;
      let startDateTimeStr = null;
      let endDateTimeStr = null;
      
      // Process start date/time using direct string manipulation
      if (azureEvent.EventStartDate) {
        // Get date as YYYY-MM-DD
        const startDateStr = azureEvent.EventStartDate.toISOString().split('T')[0];
        
        // Handle time directly as a string if possible
        let startTimeStr = '00:00:00';
        if (azureEvent.EventStartTime) {
          // Log the raw EventStartTime to help debug
          this.logger.log(`Raw EventStartTime: ${JSON.stringify(azureEvent.EventStartTime)}`);
          
          // If EventStartTime is already a string (like '19:00:00'), use it directly
          if (typeof azureEvent.EventStartTime === 'string') {
            startTimeStr = azureEvent.EventStartTime.substring(0, 8); // Take just HH:MM:SS part
            this.logger.log(`Using EventStartTime string directly: ${startTimeStr}`);
          } else {
            // If it's a Date object, extract time carefully
            startTimeStr = azureEvent.EventStartTime.toTimeString().substring(0, 8);
            this.logger.log(`Extracted time from Date object: ${startTimeStr}`);
          }
        }
        
        startDateTimeStr = `${startDateStr} ${startTimeStr}`;
        this.logger.log(`Processed merge start date/time: ${startDateTimeStr}`);
      }
      
      // Process end date/time in the same way
      if (azureEvent.EventEndDate) {
        const endDateStr = azureEvent.EventEndDate.toISOString().split('T')[0];
        
        // Handle time directly as a string if possible
        let endTimeStr = '00:00:00';
        if (azureEvent.EventEndTime) {
          // Log the raw EventEndTime to help debug
          this.logger.log(`Raw EventEndTime: ${JSON.stringify(azureEvent.EventEndTime)}`);
          
          // If EventEndTime is already a string (like '23:00:00'), use it directly
          if (typeof azureEvent.EventEndTime === 'string') {
            endTimeStr = azureEvent.EventEndTime.substring(0, 8); // Take just HH:MM:SS part
            this.logger.log(`Using EventEndTime string directly: ${endTimeStr}`);
          } else {
            // If it's a Date object, extract time carefully
            endTimeStr = azureEvent.EventEndTime.toTimeString().substring(0, 8);
            this.logger.log(`Extracted time from Date object: ${endTimeStr}`);
          }
        }
        
        endDateTimeStr = `${endDateStr} ${endTimeStr}`;
        this.logger.log(`Processed merge end date/time: ${endDateTimeStr}`);
      } else if (azureEvent.EventStartDate && azureEvent.EventEndTime) {
        // If no end date but have end time, use start date with end time
        const endDateStr = azureEvent.EventStartDate.toISOString().split('T')[0];
        
        let endTimeStr = '00:00:00';
        if (typeof azureEvent.EventEndTime === 'string') {
          endTimeStr = azureEvent.EventEndTime.substring(0, 8);
        } else {
          endTimeStr = azureEvent.EventEndTime.toTimeString().substring(0, 8);
        }
        
        endDateTimeStr = `${endDateStr} ${endTimeStr}`;
        this.logger.log(`Processed merge end date/time (using start date): ${endDateTimeStr}`);
      }
      
      // Extract image URL and media data from ProfileImageCropperValue
      let imageUrl = '';
      let mediaJson = null;
      
      if (azureEvent.ProfileImageCropperValue && typeof azureEvent.ProfileImageCropperValue === 'string') {
        try {
          // Parse the raw ProfileImageCropperValue JSON
          const imageData = JSON.parse(azureEvent.ProfileImageCropperValue);
          
          // Store the raw JSON for the media column
          mediaJson = azureEvent.ProfileImageCropperValue;
          this.logger.log(`Using raw ProfileImageCropperValue for media column: ${mediaJson}`);
          
          // Extract image URL from the parsed data
          if (imageData && imageData.Src) {
            const imagePath = imageData.Src;
            imageUrl = imagePath.startsWith('http') 
              ? imagePath 
              : `https://tucsonlovesmusic.com${imagePath}`;
            this.logger.log(`Extracted image URL: ${imageUrl}`);
          }
        } catch (e) {
          this.logger.error(`Error parsing event ProfileImageCropperValue JSON: ${e.message}`);
        }
      } else {
        this.logger.log('No ProfileImageCropperValue data found, will use empty object for media');
      }
      
      // Create the startDateTime and endDateTime strings based on raw data to ensure correct times
      const rawStartTime = typeof azureEvent.EventStartTime === 'string' ? 
        azureEvent.EventStartTime.substring(0, 8) : // Take just HH:MM:SS part
        (azureEvent.EventStartTime ? azureEvent.EventStartTime.toTimeString().substring(0, 8) : '00:00:00');
        
      const rawEndTime = typeof azureEvent.EventEndTime === 'string' ? 
        azureEvent.EventEndTime.substring(0, 8) : // Take just HH:MM:SS part
        (azureEvent.EventEndTime ? azureEvent.EventEndTime.toTimeString().substring(0, 8) : '00:00:00');
        
      // Use the original date part but with the raw time strings
      const datePart = azureEvent.EventStartDate.toISOString().split('T')[0]; // YYYY-MM-DD
      const correctedStartDateTime = `${datePart} ${rawStartTime}`;
      const correctedEndDateTime = `${datePart} ${rawEndTime}`;
      
      this.logger.log(`Corrected start date/time with original time: ${correctedStartDateTime}`);
      this.logger.log(`Corrected end date/time with original time: ${correctedEndDateTime}`);
      
      return {
        Id: azureEvent.Id,
        Name: azureEvent.Name || 'Unknown Event',
        Description: azureEvent.Description || '',
        StartDateTime: correctedStartDateTime, // Use the directly constructed string with original time
        EndDateTime: correctedEndDateTime,     // Use the directly constructed string with original time
        // Keep these for backward compatibility
        StartDateTimeStr: correctedStartDateTime,
        EndDateTimeStr: correctedEndDateTime,
        VenueId: azureEvent.VenueId,
        ImageUrl: imageUrl,
        Media: mediaJson || '{}', // Use ProfileImageCropperValue directly or empty object as fallback
        Featured: azureEvent.FeatureLevel > 0 ? (azureEvent.FeatureLevel / 100) : 0.00, // Convert to numeric 0.00-1.00
        FeaturedOrder: azureEvent.FeatureLevel,
        Url: azureEvent.Url,
        CreatedAt: azureEvent.CreatedOn ? new Date(azureEvent.CreatedOn).toISOString() : new Date().toISOString(),
        UpdatedAt: azureEvent.UpdatedOn ? new Date(azureEvent.UpdatedOn).toISOString() : new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`Error fetching Azure event data: ${error.message}`);
      return null;
    } finally {
      if (pool) {
        // Close the connection
        await pool.close();
      }
    }
  }

  /**
   * Get counts of records missing Azure IDs
   */
  @Get('validation/missing-azure/counts')
  async getMissingAzureCounts(): Promise<{ musicians: number; venues: number; events: number }> {
    try {
      this.logger.log('Getting counts of records missing Azure IDs...');
      
      // Count musicians missing Azure IDs
      const musiciansMissingAzure = await this.dataSource.query(`
        SELECT COUNT(*) as count
        FROM talent 
        WHERE azure_id IS NULL OR azure_id = ''
      `);
      
      // Count venues missing Azure IDs
      const venuesMissingAzure = await this.dataSource.query(`
        SELECT COUNT(*) as count
        FROM venue 
        WHERE azure_id IS NULL OR azure_id = ''
      `);
      
      // Count events missing Azure IDs
      const eventsMissingAzure = await this.dataSource.query(`
        SELECT COUNT(*) as count
        FROM event 
        WHERE azure_id IS NULL OR azure_id = ''
      `);
      
      const counts = {
        musicians: parseInt(musiciansMissingAzure[0]?.count || '0'),
        venues: parseInt(venuesMissingAzure[0]?.count || '0'),
        events: parseInt(eventsMissingAzure[0]?.count || '0')
      };
      
      this.logger.log(`Missing Azure ID counts: ${JSON.stringify(counts)}`);
      return counts;
      
    } catch (error) {
      this.logger.error(`Error getting missing Azure ID counts: ${error.message}`);
      throw new Error('Failed to get missing Azure ID counts');
    }
  }

  /**
   * Get musicians missing Azure IDs with potential matches
   */
  @Get('validation/missing-azure/musicians')
  async getMissingAzureMusicians() {
    try {
      this.logger.log('Getting musicians missing Azure IDs...');
      
      // Get musicians without Azure IDs
      const musicians = await this.dataSource.query(`
        SELECT 
          t.id,
          t.name,
          t.created_at as "createdAt",
          t.updated_at as "updatedAt",
          t.image_url,
          COUNT(et.event_id) as "eventCount"
        FROM talent t
        LEFT JOIN event_talents et ON t.id = et.talent_id
        WHERE t.azure_id IS NULL OR t.azure_id = ''
        GROUP BY t.id, t.name, t.created_at, t.updated_at, t.image_url
        ORDER BY "eventCount" DESC, t.updated_at DESC
        LIMIT 50
      `);
      
      // For each musician, try to find potential matches in Azure
      const musiciansWithMatches = await Promise.all(
        musicians.map(async (musician) => {
          const potentialMatches = await this.findPotentialAzureMatches(musician.name, 'musicians');
          
          return {
            id: musician.id,
            name: musician.name,
            createdAt: musician.createdAt,
            updatedAt: musician.updatedAt,
            eventCount: parseInt(musician.eventCount || '0'),
            hasImage: !!musician.image_url,
            potentialMatches
          };
        })
      );
      
      this.logger.log(`Found ${musiciansWithMatches.length} musicians missing Azure IDs`);
      return musiciansWithMatches;
      
    } catch (error) {
      this.logger.error(`Error getting musicians missing Azure IDs: ${error.message}`);
      throw new Error('Failed to get musicians missing Azure IDs');
    }
  }

  /**
   * Link Azure ID to a PostgreSQL record
   */
  @Post('validation/missing-azure/:type/link')
  async linkAzureId(
    @Param('type') type: 'musicians' | 'venues' | 'events',
    @Body() data: { recordId: string; azureId: string }
  ) {
    try {
      this.logger.log(`Linking Azure ID ${data.azureId} to ${type} record ${data.recordId}`);
      
      let tableName: string;
      let idColumn: string;
      
      switch (type) {
        case 'musicians':
          tableName = 'talent';
          idColumn = 'azure_id';
          break;
        case 'venues':
          tableName = 'venues';
          idColumn = 'azure_id';
          break;
        case 'events':
          tableName = 'events';
          idColumn = 'azure_id';
          break;
        default:
          throw new Error(`Invalid type: ${type}`);
      }
      
      // Update the record with the Azure ID
      const result = await this.dataSource.query(
        `UPDATE ${tableName} SET ${idColumn} = $1 WHERE id = $2`,
        [data.azureId, data.recordId]
      );
      
      if (result.affectedRows === 0) {
        throw new Error(`No ${type} record found with ID: ${data.recordId}`);
      }
      
      this.logger.log(`Successfully linked Azure ID ${data.azureId} to ${type} record ${data.recordId}`);
      
      return {
        success: true,
        message: `Azure ID linked successfully to ${type} record`
      };
      
    } catch (error) {
      this.logger.error(`Error linking Azure ID: ${error.message}`);
      throw new Error(`Failed to link Azure ID: ${error.message}`);
    }
  }

  /**
   * Find potential Azure matches for a given name
   */
  private async findPotentialAzureMatches(name: string, type: 'musicians' | 'venues' | 'events') {
    try {
      const pool = await this.checkAzureDbConnection();
      
      let query: string;
      let nameColumn: string;
      let idColumn: string;
      
      switch (type) {
        case 'musicians':
          query = 'SELECT Id, Name FROM Talents WHERE Name IS NOT NULL';
          nameColumn = 'Name';
          idColumn = 'Id';
          break;
        case 'venues':
          query = 'SELECT Id, Name FROM Venues WHERE Name IS NOT NULL';
          nameColumn = 'Name';
          idColumn = 'Id';
          break;
        case 'events':
          query = 'SELECT Id, Name FROM Events WHERE Name IS NOT NULL';
          nameColumn = 'Name';
          idColumn = 'Id';
          break;
        default:
          return [];
      }
      
      const result = await pool.request().query(query);
      const azureRecords = result.recordset;
      
      // Find potential matches using fuzzy matching
      const matches = [];
      const searchName = name.toLowerCase().trim();
      
      for (const record of azureRecords) {
        const azureName = record[nameColumn]?.toLowerCase().trim();
        if (!azureName) continue;
        
        let confidence = 0;
        let matchType: 'exact' | 'fuzzy' | 'email' | 'phone' | 'website' = 'fuzzy';
        
        // Exact match
        if (azureName === searchName) {
          confidence = 1.0;
          matchType = 'exact';
        }
        // Contains match
        else if (azureName.includes(searchName) || searchName.includes(azureName)) {
          const longer = azureName.length > searchName.length ? azureName : searchName;
          const shorter = azureName.length <= searchName.length ? azureName : searchName;
          confidence = shorter.length / longer.length;
          matchType = 'fuzzy';
        }
        // Word overlap
        else {
          const searchWords = searchName.split(/\s+/);
          const azureWords = azureName.split(/\s+/);
          const commonWords = searchWords.filter(word => 
            azureWords.some(azureWord => 
              azureWord.includes(word) || word.includes(azureWord)
            )
          );
          
          if (commonWords.length > 0) {
            confidence = commonWords.length / Math.max(searchWords.length, azureWords.length);
            matchType = 'fuzzy';
          }
        }
        
        // Only include matches with reasonable confidence
        if (confidence >= 0.3) {
          matches.push({
            azureId: record[idColumn],
            azureName: record[nameColumn],
            confidence,
            matchType
          });
        }
      }
      
      // Sort by confidence descending and return top 5
      matches.sort((a, b) => b.confidence - a.confidence);
      
      await pool.close();
      return matches.slice(0, 5);
      
    } catch (error) {
      this.logger.error(`Error finding potential Azure matches: ${error.message}`);
      return [];
    }
  }
}
