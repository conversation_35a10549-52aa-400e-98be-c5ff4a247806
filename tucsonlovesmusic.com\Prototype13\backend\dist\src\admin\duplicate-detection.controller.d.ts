import { DataSource } from 'typeorm';
interface RecordMetadata {
    id: string;
    name: string;
    eventCount?: number;
    talentCount?: number;
    completenessScore: number;
    updatedAt: string;
    createdAt: string;
    hasDescription: boolean;
    hasImage: boolean;
    hasAddress?: boolean;
    hasGenre?: boolean;
    hasTicketInfo?: boolean;
    googlePlaceId?: string;
    venueId?: string;
    azureId?: string;
}
interface DuplicateGroup {
    name: string;
    count: number;
    ids: string[];
    recordMetadata?: RecordMetadata[];
}
interface DuplicateEvent extends DuplicateGroup {
    venue_id: string;
    event_date: string;
    event_datetime: string;
}
declare class MergeDuplicatesDto {
    primaryId: string;
    duplicateIds: string[];
}
export declare class DuplicateDetectionController {
    private dataSource;
    constructor(dataSource: DataSource);
    findDuplicateVenues(): Promise<DuplicateGroup[]>;
    findDuplicateMusicians(): Promise<DuplicateGroup[]>;
    findDuplicateEvents(): Promise<DuplicateEvent[]>;
    getDuplicateCounts(): Promise<{
        venues: number;
        musicians: number;
        events: number;
    }>;
    mergeVenues(mergeDto: MergeDuplicatesDto): Promise<{
        success: boolean;
        message: string;
    }>;
    mergeMusicians(mergeDto: MergeDuplicatesDto): Promise<{
        success: boolean;
        message: string;
    }>;
    batchMergeEvents(): Promise<{
        success: boolean;
        message: string;
    }>;
    private selectPrimaryRecord;
    private mergeEventGroup;
    mergeEvents(mergeDto: MergeDuplicatesDto): Promise<{
        success: boolean;
        message: string;
    }>;
}
export {};
