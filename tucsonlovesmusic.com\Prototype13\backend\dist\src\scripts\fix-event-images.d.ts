import { DataSource } from 'typeorm';
export interface EventImageFixResult {
    total: number;
    processed: number;
    success: number;
    skipped: number;
    errors: number;
    fixedEvents?: Array<{
        id: string;
        name: string;
        imageUrl: string;
    }>;
}
export declare function processEventImagesFromAzure(dataSource: DataSource, dryRun?: boolean): Promise<EventImageFixResult>;
