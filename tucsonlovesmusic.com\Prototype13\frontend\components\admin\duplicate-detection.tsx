"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Calendar, Loader2, AlertCircle, Users, MapPin, Copy, ChevronDown, ChevronRight, MergeIcon, Check, Link } from "lucide-react";
import { useAuth } from "@/hooks/use-auth";
import { 
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { getApiUrl } from "@/lib/config";


interface RecordMetadata {
  id: string;
  name: string;
  completenessScore: number;
  updatedAt: string;
  createdAt: string;
  eventCount?: number;
  talentCount?: number;
  hasDescription: boolean;
  hasImage: boolean;
  hasAddress?: boolean;
  hasGenre?: boolean;
  hasTicketInfo?: boolean;
  googlePlaceId?: string;
  venueId?: string;
  azureId?: string;
}

interface DuplicateGroup {
  name: string;
  count: number;
  ids: string[];
  normalized_name?: string;
  recordMetadata?: RecordMetadata[];
}

interface DuplicateEvent extends DuplicateGroup {
  venue_id: string;
  event_date: string;
  event_datetime: string;
}

interface DuplicateData {
  venues: DuplicateGroup[];
  musicians: DuplicateGroup[];
  events: DuplicateEvent[];
}

interface MergeDialogProps {
  type: 'venues' | 'musicians' | 'events';
  duplicateGroup: DuplicateGroup;
  onMerge: () => void;
}

interface DuplicateCounts {
  venues: number;
  musicians: number;
  events: number;
}



export function DuplicateDetection() {
  const router = useRouter();
  const [duplicateCounts, setDuplicateCounts] = useState<DuplicateCounts | null>(null);
  const [duplicateData, setDuplicateData] = useState<DuplicateData>({ venues: [], musicians: [], events: [] });

  const [isLoading, setIsLoading] = useState(true);
  const [isBatchMerging, setIsBatchMerging] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expanded, setExpanded] = useState<{ 
    venues: boolean; 
    musicians: boolean; 
    events: boolean;
  }>({ 
    venues: false, 
    musicians: false, 
    events: false
  });
  const { getAccessToken } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    const fetchDuplicateData = async () => {
      try {
        setIsLoading(true);
        const token = await getAccessToken();
        
        // Use getApiUrl() directly to ensure we get the correct URL
        const baseUrl = getApiUrl();
        console.log('Duplicate detection using API URL:', baseUrl);
        
        // Fetch duplicate counts
        const countsResponse = await fetch(`${baseUrl}/admin/duplicates`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!countsResponse.ok) {
          throw new Error('Failed to fetch duplicate counts');
        }

        const countsData = await countsResponse.json();
        console.log('Duplicate counts data:', countsData);
        setDuplicateCounts(countsData);
        

        
        // Fetch venue duplicates
        if (countsData.venues > 0) {
          const venueResponse = await fetch(`${baseUrl}/admin/duplicates/venues`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          
          if (venueResponse.ok) {
            const venueData = await venueResponse.json();
            setDuplicateData(prev => ({ ...prev, venues: venueData }));
          }
        }
        
        // Fetch musician duplicates
        if (countsData.musicians > 0) {
          const musicianResponse = await fetch(`${baseUrl}/admin/duplicates/musicians`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          
          if (musicianResponse.ok) {
            const musicianData = await musicianResponse.json();
            console.log('Raw musician duplicate data:', musicianData);
            
            // Validate data structure and metadata
            if (Array.isArray(musicianData)) {
              const hasMetadata = musicianData.some(group => 
                group.recordMetadata && Array.isArray(group.recordMetadata) && group.recordMetadata.length > 0
              );
              console.log('Musician data has metadata:', hasMetadata);
              
              // Check first group's metadata in detail
              if (musicianData.length > 0 && musicianData[0].recordMetadata) {
                console.log('First musician group name:', musicianData[0].name);
                console.log('First musician group metadata sample:', musicianData[0].recordMetadata[0]);
              }
            }
            
            setDuplicateData(prev => ({ ...prev, musicians: musicianData }));
          }
        }
        
        // Fetch event duplicates
        if (countsData.events > 0) {
          const eventResponse = await fetch(`${baseUrl}/admin/duplicates/events`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          
          if (eventResponse.ok) {
            const eventData = await eventResponse.json();
            setDuplicateData(prev => ({ ...prev, events: eventData }));
          }
        }
      } catch (err) {
        console.error('Duplicate data fetch error:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setIsLoading(false);
      }
    };

    fetchDuplicateData();
  }, [getAccessToken]);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Loader2 className="h-5 w-5 animate-spin" /> Deduplication
          </CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center">
          <p className="text-muted-foreground">Analyzing database for duplicates...</p>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-500">
            <AlertCircle className="h-5 w-5" /> Duplicate Detection Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-500">{error}</p>
        </CardContent>
      </Card>
    );
  }

  // Toggle the expanded state for a specific section
  const toggleExpanded = (section: 'venues' | 'musicians' | 'events') => {
    setExpanded(prev => ({ ...prev, [section]: !prev[section] }));
  };

  // Handle linking missing Azure IDs
  const handleLinkAzureId = async (recordId: string, azureId: string, type: 'musicians' | 'venues' | 'events') => {
    try {
      const token = await getAccessToken();
      const baseUrl = getApiUrl();
      
      const response = await fetch(`${baseUrl}/admin/id-validation/validation/missing-azure/${type}/link`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          recordId,
          azureId
        })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to link Azure ID for ${type}`);
      }
      
      const result = await response.json();
      
      toast({
        title: 'Success',
        description: result.message || `Azure ID linked successfully`,
        variant: 'default',
        duration: 5000,
      });
      
      // Refresh the data
      await refreshData();
      
      return true;
      
    } catch (err) {
      console.error('Link Azure ID error:', err);
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to link Azure ID',
        variant: 'destructive',
        duration: 5000,
      });
      
      return false;
    }
  };

  // Refresh data after a merge operation
  const refreshData = async () => {
    setIsLoading(true);
    try {
      const token = await getAccessToken();
      
      // Use getApiUrl() to ensure we get the correct API URL
      const baseUrl = getApiUrl();
      console.log('refreshData using API URL:', baseUrl);
      
      // Fetch counts
      const countsResponse = await fetch(`${baseUrl}/admin/duplicates`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!countsResponse.ok) {
        throw new Error('Failed to fetch duplicate counts');
      }

      const countsData = await countsResponse.json();
      setDuplicateCounts(countsData);
      
      // Clear existing data and fetch fresh data
      setDuplicateData({ venues: [], musicians: [], events: [] });
      
      if (countsData.venues > 0) {
        const venueResponse = await fetch(`${baseUrl}/admin/duplicates/venues`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (venueResponse.ok) {
          const venueData = await venueResponse.json();
          setDuplicateData(prev => ({ ...prev, venues: venueData }));
        }
      }
      
      if (countsData.musicians > 0) {
        const musicianResponse = await fetch(`${baseUrl}/admin/duplicates/musicians`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (musicianResponse.ok) {
          const musicianData = await musicianResponse.json();
          console.log('Refresh: Raw musician duplicate data:', musicianData);
          
          // Validate data structure and metadata
          if (Array.isArray(musicianData)) {
            const hasMetadata = musicianData.some(group => 
              group.recordMetadata && Array.isArray(group.recordMetadata) && group.recordMetadata.length > 0
            );
            console.log('Refresh: Musician data has metadata:', hasMetadata);
            
            // Check first group's metadata in detail
            if (musicianData.length > 0 && musicianData[0].recordMetadata) {
              console.log('Refresh: First musician group name:', musicianData[0].name);
              console.log('Refresh: First musician group metadata sample:', musicianData[0].recordMetadata[0]);
            }
          }
          
          setDuplicateData(prev => ({ ...prev, musicians: musicianData }));
        }
      }
      
      if (countsData.events > 0) {
        const eventResponse = await fetch(`${baseUrl}/admin/duplicates/events`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (eventResponse.ok) {
          const eventData = await eventResponse.json();
          setDuplicateData(prev => ({ ...prev, events: eventData }));
        }
      }
    } catch (err) {
      console.error('Refresh data error:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle merging duplicates
  const handleMerge = async (type: 'venues' | 'musicians' | 'events', primaryId: string, duplicateIds: string[]) => {
    try {
      const token = await getAccessToken();
      // Use getApiUrl() to ensure we get the correct API URL
      const baseUrl = getApiUrl();
      console.log('handleMerge using API URL:', baseUrl);
      const response = await fetch(`${baseUrl}/admin/duplicates/${type}/merge`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          primaryId,
          duplicateIds
        })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to merge ${type}`);
      }
      
      const result = await response.json();
      
      // Show toast notification with enough duration to be visible
      toast({
        title: 'Success',
        description: result.message || `${type} merged successfully`,
        variant: 'default',
        duration: 5000, // Give users enough time to see the notification
      });
      
      // Refresh the data
      await refreshData();
      
      return true; // Return success status
      
    } catch (err) {
      console.error('Merge error:', err);
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to merge duplicates',
        variant: 'destructive',
        duration: 5000,
      });
      
      return false; // Return failure status
    }
  };

  // Handle batch merging all duplicate events
  const handleBatchMergeEvents = async () => {
    if (duplicateData.events.length === 0) return;
    
    setIsBatchMerging(true);
    try {
      const token = await getAccessToken();
      const baseUrl = getApiUrl();
      
      const response = await fetch(`${baseUrl}/admin/duplicates/events/batch-merge`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to batch merge events');
      }
      
      const result = await response.json();
      
      toast({
        title: 'Batch Merge Complete',
        description: result.message || 'All duplicate events have been merged successfully',
        variant: 'default',
        duration: 7000,
      });
      
      // Refresh the data
      await refreshData();
      
    } catch (err) {
      console.error('Batch merge error:', err);
      toast({
        title: 'Batch Merge Failed',
        description: err instanceof Error ? err.message : 'Failed to batch merge events',
        variant: 'destructive',
        duration: 7000,
      });
    } finally {
       setIsBatchMerging(false);
     }
  };

  // Icon should only reflect duplicate data, not Azure ID validation (which has its own component)
  const isClean = (!duplicateCounts?.events && !duplicateCounts?.musicians && !duplicateCounts?.venues);
  console.log('Deduplication icon debug:', {
    duplicateCounts,
    isClean,
    duplicateConditions: {
      'duplicateCounts?.events': duplicateCounts?.events,
      'duplicateCounts?.musicians': duplicateCounts?.musicians,
      'duplicateCounts?.venues': duplicateCounts?.venues,
    }
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Copy className={`h-5 w-5 ${isClean ? 'text-green-500' : 'text-amber-500'}`} /> Deduplication
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">


        {/* Events Section */}
        <Collapsible 
          open={expanded.events} 
          onOpenChange={() => toggleExpanded('events')}
          className={`rounded-md ${duplicateCounts?.events ? 'border-amber-200' : 'border-green-200'}`}
        >
          <CollapsibleTrigger asChild>
            <div className="flex items-center justify-between p-3 cursor-pointer hover:bg-muted/50">
              <div className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                <span>Events</span>
              </div>
              <div className="flex items-center gap-2">
                <div className={`font-semibold ${duplicateCounts?.events ? 'text-amber-500' : 'text-green-500'}`}>
                  {duplicateCounts?.events ? `${duplicateCounts.events} duplicate groups` : 'No duplicates'}
                </div>
                {expanded.events ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
              </div>
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent>
            {duplicateData.events.length > 0 ? (
              <div className="p-3 border-t">
                <div className="flex justify-between items-center mb-4">
                  <div className="text-sm text-muted-foreground">
                    {duplicateData.events.length} duplicate groups found
                  </div>
                  <Button 
                    onClick={handleBatchMergeEvents}
                    disabled={isBatchMerging || duplicateData.events.length === 0}
                    variant="outline"
                    size="sm"
                    className="gap-2"
                  >
                    {isBatchMerging ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <MergeIcon className="h-4 w-4" />
                    )}
                    {isBatchMerging ? 'Merging...' : 'Batch Merge All'}
                  </Button>
                </div>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Event Name</TableHead>
                      <TableHead>Date & Time</TableHead>
                      <TableHead className="text-right">Duplicates</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {duplicateData.events.map((event, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">{event.name}</TableCell>
                        <TableCell>
                          {event.event_datetime ? 
                            new Date(event.event_datetime).toLocaleString() : 
                            new Date(event.event_date).toLocaleDateString()
                          }
                        </TableCell>
                        <TableCell className="text-right">{event.count}</TableCell>
                        <TableCell className="text-right">
                          <MergeDialog 
                            type="events" 
                            duplicateGroup={event} 
                            onMerge={() => refreshData()} 
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="px-3 pb-3 italic text-muted-foreground text-sm">No duplicate events found</div>
            )}
          </CollapsibleContent>
        </Collapsible>

        {/* Musicians Section */}
        <Collapsible 
          open={expanded.musicians} 
          onOpenChange={() => toggleExpanded('musicians')}
          className={`rounded-md ${duplicateCounts?.musicians ? 'border-amber-200' : 'border-green-200'}`}
        >
          <CollapsibleTrigger asChild>
            <div className="flex items-center justify-between p-3 cursor-pointer hover:bg-muted/50">
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                <span>Musicians</span>
              </div>
              <div className="flex items-center gap-2">
                <div className={`font-semibold ${duplicateCounts?.musicians ? 'text-amber-500' : 'text-green-500'}`}>
                  {duplicateCounts?.musicians ? `${duplicateCounts.musicians} duplicate groups` : 'No duplicates'}
                </div>
                {expanded.musicians ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
              </div>
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent>
            {duplicateData.musicians.length > 0 ? (
              <div className="p-3 border-t">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Musician Name</TableHead>
                      <TableHead className="text-right">Duplicates</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {duplicateData.musicians.map((musician, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">{musician.name}</TableCell>
                        <TableCell className="text-right">{musician.count}</TableCell>
                        <TableCell className="text-right">
                          <MergeDialog 
                            type="musicians" 
                            duplicateGroup={musician} 
                            onMerge={() => refreshData()} 
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="px-3 pb-3 italic text-muted-foreground text-sm">No duplicate musicians found</div>
            )}
          </CollapsibleContent>
        </Collapsible>

        {/* Venues Section */}
        <Collapsible 
          open={expanded.venues} 
          onOpenChange={() => toggleExpanded('venues')}
          className={`rounded-md ${duplicateCounts?.venues ? 'border-amber-200' : 'border-green-200'}`}
        >
          <CollapsibleTrigger asChild>
            <div className="flex items-center justify-between p-3 cursor-pointer hover:bg-muted/50">
              <div className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                <span>Venues</span>
              </div>
              <div className="flex items-center gap-2">
                <div className={`font-semibold ${duplicateCounts?.venues ? 'text-amber-500' : 'text-green-500'}`}>
                  {duplicateCounts?.venues ? `${duplicateCounts.venues} duplicate groups` : 'No duplicates'}
                </div>
                {expanded.venues ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
              </div>
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent>
            {duplicateData.venues.length > 0 ? (
              <div className="p-3 border-t">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Venue Name</TableHead>
                      <TableHead className="text-right">Duplicates</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {duplicateData.venues.map((venue, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">{venue.name}</TableCell>
                        <TableCell className="text-right">{venue.count}</TableCell>
                        <TableCell className="text-right">
                          <MergeDialog 
                            type="venues" 
                            duplicateGroup={venue} 
                            onMerge={() => refreshData()} 
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="px-3 pb-3 italic text-muted-foreground text-sm">No duplicate venues found</div>
            )}
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  );
}



// MergeDialog component code
function MergeDialog({ type, duplicateGroup, onMerge }: MergeDialogProps) {
  const [open, setOpen] = useState(false);
  const [primaryId, setPrimaryId] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { getAccessToken } = useAuth();
  
  // Reset primary ID when dialog opens or group changes
  useEffect(() => {
    if (open && duplicateGroup.ids && duplicateGroup.ids.length > 0) {
      setPrimaryId(duplicateGroup.ids[0]);
    }
  }, [open, duplicateGroup]);

  const handleMerge = async () => {
    if (!primaryId) return;
    
    setIsLoading(true);
    try {
      const token = await getAccessToken();
      // Get the list of all duplicate IDs except the primary
      const duplicateIds = duplicateGroup.ids.filter(id => id !== primaryId);
      
      const response = await fetch(`${getApiUrl()}/admin/duplicates/${type}/merge`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          primaryId,
          duplicateIds,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to merge ${type}`);
      }

      toast({
        title: "Success",
        description: `${type.charAt(0).toUpperCase() + type.slice(1)} merged successfully`,
      });
      
      setOpen(false);
      onMerge();
    } catch (error) {
      console.error(`Error merging ${type}:`, error);
      toast({
        title: "Error",
        description: `Failed to merge ${type}. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getTypeName = () => {
     switch (type) {
       case 'venues':
         return 'Venue';
       case 'musicians':
         return 'Musician';
       case 'events':
         return 'Event';
       default:
         return 'Record';
     }
   };

   return (
     <Dialog open={open} onOpenChange={setOpen}>
       <DialogTrigger asChild>
         <Button variant="outline" size="sm">
           <MergeIcon className="mr-2 h-4 w-4" />
           Merge
         </Button>
       </DialogTrigger>
       <DialogContent className="max-w-md">
         <DialogHeader>
           <DialogTitle>Merge Duplicate {getTypeName()} Records</DialogTitle>
           <DialogDescription>
             Select the primary record to keep. Data from other records will be merged into this one.
           </DialogDescription>
         </DialogHeader>
         
         <div className="my-6 space-y-4">
           <div className="mb-4">
             <label className="text-sm font-semibold block mb-2">Select primary record to keep</label>
             <Select value={primaryId} onValueChange={setPrimaryId}>
               <SelectTrigger>
                 <SelectValue placeholder="Select primary record" />
               </SelectTrigger>
               <SelectContent>
                 {duplicateGroup.ids?.map((id, index) => {
                   // Find metadata for this record if available
                   const metadata = duplicateGroup.recordMetadata?.find(m => m.id === id);
                   
                   // Generate contextual descriptions based on metadata
                   let contextInfo = [];
                   if (metadata) {
                     // Format dates consistently
                     const formatDate = (dateString?: string): string | undefined => {
                       if (!dateString) return undefined;
                       const date = new Date(dateString);
                       return date.toLocaleDateString('en-US', {
                         month: '2-digit',
                         day: '2-digit',
                         year: '2-digit'
                       });
                     };
                     
                     // Add created date info
                     if (metadata.createdAt) {
                       contextInfo.push(`created: ${formatDate(metadata.createdAt)}`);
                     }
                     
                     // Add updated date info
                     if (metadata.updatedAt) {
                       contextInfo.push(`updated: ${formatDate(metadata.updatedAt)}`);
                     }
                     
                     // Add image info
                     if (metadata.hasImage) {
                       contextInfo.push(`has image`);
                     } else {
                       contextInfo.push(`no image`);
                     }
                     
                     // Add description info
                     if (metadata.hasDescription) {
                       contextInfo.push(`has description`);
                     }
                     
                     // For venues, show Google Place ID status
                     if (type === 'venues') {
                       if (metadata.googlePlaceId) {
                         contextInfo.push(`has Google Place ID`);
                       } else {
                         contextInfo.push(`no Google Place ID`);
                       }
                     }
                   }
                   
                   return (
                     <SelectItem key={id} value={id} className="py-2">
                       <div>
                         <div className="font-medium">Record {index + 1} (ID: {typeof id === 'string' ? id.slice(-8) : id})</div>
                         {contextInfo.length > 0 && (
                           <div className="text-xs text-muted-foreground mt-1">
                             {contextInfo.join(", ")}
                           </div>
                         )}
                       </div>
                     </SelectItem>
                   );
                 })}
               </SelectContent>
             </Select>
           </div>
           
           <div className="space-y-1">
             <h4 className="text-sm font-semibold">What happens during merge:</h4>
             <ul className="text-sm text-muted-foreground list-disc pl-5 space-y-1">
               <li>Empty fields in the primary record will be filled with data from duplicates</li>
               <li>Related records will be updated to point to the primary record</li>
               <li>Duplicate records will be marked as deleted</li>
             </ul>
           </div>
         </div>
         
         <DialogFooter>
           <Button variant="outline" onClick={() => setOpen(false)}>Cancel</Button>
           <Button 
             onClick={handleMerge} 
             disabled={!primaryId || isLoading}
           >
             {isLoading ? (
               <>
                 <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                 Merging...
               </>
             ) : (
               <>
                 Merge Duplicates
               </>
             )}
           </Button>
         </DialogFooter>
       </DialogContent>
     </Dialog>
   );
}
