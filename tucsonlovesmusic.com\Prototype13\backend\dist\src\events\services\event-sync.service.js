"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var EventSyncService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventSyncService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_sync_log_entity_1 = require("../entities/event-sync-log.entity");
const schedule_1 = require("@nestjs/schedule");
const migration_modules_1 = require("../../database/migration-modules");
const sql = __importStar(require("mssql"));
let EventSyncService = EventSyncService_1 = class EventSyncService {
    constructor(eventSyncLogRepository, dataSource) {
        this.eventSyncLogRepository = eventSyncLogRepository;
        this.dataSource = dataSource;
        this.logger = new common_1.Logger(EventSyncService_1.name);
        this.migrationOrchestrator = new migration_modules_1.MigrationOrchestrator(this.dataSource, {
            skipDuplicates: true,
            enableImageProcessing: true,
            enableFuzzyDuplicateDetection: true,
            dryRun: false
        });
    }
    async runHourlySync() {
        try {
            this.logger.log('Starting scheduled 12-hourly event sync');
            const runningSync = await this.eventSyncLogRepository.findOne({
                where: { status: 'running' }
            });
            if (runningSync) {
                const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
                if (runningSync.startTime < thirtyMinutesAgo) {
                    this.logger.warn(`Found a stuck sync job from ${runningSync.startTime.toISOString()}. Marking as failed and starting new sync.`);
                    runningSync.status = 'failed';
                    runningSync.endTime = new Date();
                    runningSync.error = 'Sync job timed out after 30 minutes';
                    await this.eventSyncLogRepository.save(runningSync);
                }
                else {
                    this.logger.log(`Skipping 12-hourly sync as another sync job is already running since ${runningSync.startTime.toISOString()}`);
                    return;
                }
            }
            await this.triggerSync();
        }
        catch (error) {
            this.logger.error(`12-hourly event sync failed: ${error.message}`, error.stack);
        }
    }
    async getLatestSync() {
        const logs = await this.eventSyncLogRepository.find({
            order: { startTime: 'DESC' },
            take: 1
        });
        return logs.length > 0 ? logs[0] : null;
    }
    async getSyncHistory(limit = 10) {
        return this.eventSyncLogRepository.find({
            order: { startTime: 'DESC' },
            take: limit
        });
    }
    async triggerManualSync() {
        this.logger.log('Manual event sync triggered');
        return this.triggerSync();
    }
    async syncEventTalents() {
        this.logger.log('Event talents sync triggered');
        const syncLog = this.eventSyncLogRepository.create({
            startTime: new Date(),
            status: 'running',
            eventsFound: 0,
            eventsImported: 0,
            eventsSkipped: 0
        });
        await this.eventSyncLogRepository.save(syncLog);
        try {
            const shouldRunSync = process.env.ENABLE_EVENT_SYNC !== 'false';
            if (!shouldRunSync) {
                this.logger.log('Event talents sync disabled by environment variable. Recording sync log but not running actual sync.');
                const simulatedResult = {
                    talentsFound: 0,
                    talentsImported: 0,
                    talentsSkipped: 0,
                    newTalents: [],
                    skippedTalents: [],
                    imagesUploaded: [],
                    imagesFailed: []
                };
                syncLog.endTime = new Date();
                syncLog.status = 'completed';
                syncLog.eventsFound = 0;
                syncLog.eventsImported = 0;
                syncLog.eventsSkipped = 0;
                syncLog.lastSyncDate = new Date();
                syncLog.details = {
                    newEvents: [],
                    skippedEvents: [],
                    imagesUploaded: [],
                    imagesFailed: [],
                    updatedEvents: [],
                    connectionStatus: { postgresql: true, azure: true },
                    syncDisabled: true
                };
                await this.eventSyncLogRepository.save(syncLog);
                return syncLog;
            }
            const { migrateNewAzureTalents } = require('../../database/migrate-new-azure-talents');
            const result = await migrateNewAzureTalents('talent', false, true);
            syncLog.endTime = new Date();
            syncLog.status = 'completed';
            syncLog.eventsFound = result.talentsFound || 0;
            syncLog.eventsImported = result.talentsImported || 0;
            syncLog.eventsSkipped = result.talentsSkipped || 0;
            syncLog.lastSyncDate = new Date();
            const details = {
                newEvents: result.newTalents || [],
                skippedEvents: result.skippedTalents?.map(t => ({ name: t.name, reason: t.reason })) || [],
                imagesUploaded: result.imagesUploaded || [],
                imagesFailed: result.imagesFailed?.map(i => ({ name: i.name, reason: i.reason })) || [],
                updatedEvents: [],
                connectionStatus: { postgresql: true, azure: true }
            };
            syncLog.details = details;
            await this.eventSyncLogRepository.save(syncLog);
            return syncLog;
        }
        catch (error) {
            syncLog.endTime = new Date();
            syncLog.status = 'failed';
            syncLog.error = error.message || 'Unknown error occurred during event talents sync';
            await this.eventSyncLogRepository.save(syncLog);
            throw error;
        }
    }
    async triggerSync() {
        const syncLog = this.eventSyncLogRepository.create({
            startTime: new Date(),
            status: 'running',
            eventsFound: 0,
            eventsImported: 0,
            eventsSkipped: 0
        });
        await this.eventSyncLogRepository.save(syncLog);
        try {
            const shouldRunSync = process.env.ENABLE_EVENT_SYNC !== 'false';
            if (!shouldRunSync) {
                this.logger.log('Event sync disabled by environment variable. Recording sync log but not running actual sync.');
                const simulatedResult = {
                    eventsFound: 0,
                    eventsImported: 0,
                    eventsSkipped: 0,
                    lastSyncDate: new Date(),
                    newEvents: [],
                    skippedEvents: [],
                    imagesUploaded: [],
                    imagesFailed: [],
                    updatedEvents: [],
                    unaccountedEvents: [],
                    connectionStatus: { postgresql: true, azure: true }
                };
                syncLog.endTime = new Date();
                syncLog.status = 'completed';
                syncLog.lastSyncDate = simulatedResult.lastSyncDate;
                syncLog.details = {
                    ...simulatedResult,
                    syncDisabled: true
                };
                await this.eventSyncLogRepository.save(syncLog);
                return syncLog;
            }
            const azureConfig = {
                server: process.env.AZURE_DB_SERVER || 'mssql.drv1.umbhost.net',
                database: process.env.AZURE_DB_NAME || 'TLM',
                user: process.env.AZURE_DB_USER || 'Reader',
                password: process.env.AZURE_DB_PASSWORD || 'TLM1234!',
                options: {
                    encrypt: false
                }
            };
            const azureConnection = await sql.connect(azureConfig);
            try {
                const syncPromise = this.migrationOrchestrator.migrateNewAzureEvents(azureConnection, false);
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => {
                        reject(new Error('Event sync operation timed out after 25 minutes'));
                    }, 25 * 60 * 1000);
                });
                const migrationResult = await Promise.race([syncPromise, timeoutPromise]);
                const result = {
                    eventsFound: migrationResult.totalProcessed,
                    eventsImported: migrationResult.created + migrationResult.updated,
                    eventsSkipped: migrationResult.skipped,
                    lastSyncDate: new Date(),
                    newEvents: [],
                    skippedEvents: [],
                    imagesUploaded: [],
                    imagesFailed: [],
                    updatedEvents: [],
                    unaccountedEvents: [],
                    connectionStatus: { postgresql: true, azure: true }
                };
                syncLog.endTime = new Date();
                syncLog.status = 'completed';
                syncLog.eventsFound = result.eventsFound || 0;
                syncLog.eventsImported = result.eventsImported || 0;
                syncLog.eventsSkipped = result.eventsSkipped || 0;
                syncLog.lastSyncDate = result.lastSyncDate || null;
                const details = {
                    newEvents: result.newEvents || [],
                    skippedEvents: result.skippedEvents || [],
                    imagesUploaded: result.imagesUploaded || [],
                    imagesFailed: result.imagesFailed || [],
                    updatedEvents: result.updatedEvents || [],
                    connectionStatus: result.connectionStatus || { postgresql: true, azure: true }
                };
                if (result.eventsFound > (result.eventsImported + result.eventsSkipped)) {
                    details.unaccountedEvents = result.unaccountedEvents || [];
                }
                syncLog.details = details;
                await this.eventSyncLogRepository.save(syncLog);
                try {
                    this.logger.log('Running automatic date/time fix after sync...');
                    const fixResult = await this.fixEventDatetimes();
                    this.logger.log(`Date/time fix completed successfully. Fixed ${fixResult.eventsImported} of ${fixResult.eventsFound} events.`);
                    syncLog.notes = `Automatic date/time fix ran after sync and fixed ${fixResult.eventsImported} events.`;
                    await this.eventSyncLogRepository.save(syncLog);
                }
                catch (fixError) {
                    this.logger.error(`Error during automatic date/time fix: ${fixError.message}`, fixError.stack);
                    syncLog.notes = `Automatic date/time fix attempted but failed: ${fixError.message}`;
                    await this.eventSyncLogRepository.save(syncLog);
                }
                return syncLog;
            }
            catch (syncError) {
                this.logger.error(`Event sync failed: ${syncError.message}`, syncError.stack);
                throw syncError;
            }
            finally {
                try {
                    await azureConnection.close();
                }
                catch (closeError) {
                    this.logger.error(`Error closing Azure connection: ${closeError.message}`);
                }
            }
        }
        catch (error) {
            syncLog.endTime = new Date();
            syncLog.status = 'failed';
            syncLog.error = error.message || 'Unknown error occurred during event sync';
            this.logger.error(`Event sync failed: ${error.message}`, error.stack);
            await this.eventSyncLogRepository.save(syncLog);
            throw error;
        }
    }
    async fixEventDatetimes() {
        this.logger.log('Event datetime fix triggered');
        const syncLog = this.eventSyncLogRepository.create({
            startTime: new Date(),
            status: 'running',
            eventsFound: 0,
            eventsImported: 0,
            eventsSkipped: 0
        });
        await this.eventSyncLogRepository.save(syncLog);
        try {
            const { fixEventDatetimes } = require('../../database/fix-event-datetimes');
            const result = await fixEventDatetimes(false);
            syncLog.endTime = new Date();
            syncLog.status = 'completed';
            syncLog.eventsFound = result.eventsChecked || 0;
            syncLog.eventsImported = result.eventsFixed || 0;
            syncLog.eventsSkipped = result.eventsSkipped || 0;
            syncLog.lastSyncDate = new Date();
            const details = {
                newEvents: [],
                skippedEvents: [],
                imagesUploaded: [],
                imagesFailed: [],
                updatedEvents: result.fixedEvents || [],
                connectionStatus: { postgresql: true, azure: true },
                syncDisabled: false
            };
            syncLog.error = `Fixed ${result.eventsFixed || 0} event datetime issues out of ${result.eventsChecked || 0} events checked.`;
            syncLog.details = details;
            await this.eventSyncLogRepository.save(syncLog);
            return syncLog;
        }
        catch (error) {
            syncLog.endTime = new Date();
            syncLog.status = 'failed';
            syncLog.error = error.message || 'Unknown error occurred during event datetime fix';
            await this.eventSyncLogRepository.save(syncLog);
            throw error;
        }
    }
    async fixEventImages() {
        this.logger.log('Starting event image fix process');
        const syncLog = new event_sync_log_entity_1.EventSyncLog();
        syncLog.startTime = new Date();
        syncLog.status = 'running';
        syncLog.notes = 'Event image fix operation';
        syncLog.details = {
            newEvents: [],
            skippedEvents: [],
            imagesUploaded: [],
            imagesFailed: [],
            updatedEvents: [],
            connectionStatus: { postgresql: true, azure: true },
            syncDisabled: false
        };
        await this.eventSyncLogRepository.save(syncLog);
        try {
            const { processEventImagesFromAzure } = require('../../scripts/fix-event-images');
            const result = await processEventImagesFromAzure(this.dataSource, false);
            syncLog.endTime = new Date();
            syncLog.status = 'completed';
            syncLog.eventsFound = result.total || 0;
            syncLog.eventsImported = result.success || 0;
            syncLog.eventsSkipped = result.skipped || 0;
            syncLog.lastSyncDate = new Date();
            const details = {
                newEvents: [],
                skippedEvents: [],
                imagesUploaded: result.fixedEvents || [],
                imagesFailed: [],
                updatedEvents: result.fixedEvents || [],
                connectionStatus: { postgresql: true, azure: true },
                syncDisabled: false
            };
            syncLog.details = details;
            syncLog.error = null;
            this.logger.log(`Event image fix completed: ${result.success} images processed successfully`);
            return syncLog;
        }
        catch (error) {
            this.logger.error('Error during event image fix:', error);
            syncLog.endTime = new Date();
            syncLog.status = 'failed';
            syncLog.error = error instanceof Error ? error.message : 'Unknown error during image fix';
            await this.eventSyncLogRepository.save(syncLog);
            throw error;
        }
        finally {
            await this.eventSyncLogRepository.save(syncLog);
        }
    }
};
exports.EventSyncService = EventSyncService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_12_HOURS),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EventSyncService.prototype, "runHourlySync", null);
exports.EventSyncService = EventSyncService = EventSyncService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(event_sync_log_entity_1.EventSyncLog)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.DataSource])
], EventSyncService);
//# sourceMappingURL=event-sync.service.js.map