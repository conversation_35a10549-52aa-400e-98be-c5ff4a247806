"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/is-core-module";
exports.ids = ["vendor-chunks/is-core-module"];
exports.modules = {

/***/ "(instrument)/./node_modules/is-core-module/core.json":
/*!***********************************************!*\
  !*** ./node_modules/is-core-module/core.json ***!
  \***********************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"assert":true,"node:assert":[">= 14.18 && < 15",">= 16"],"assert/strict":">= 15","node:assert/strict":">= 16","async_hooks":">= 8","node:async_hooks":[">= 14.18 && < 15",">= 16"],"buffer_ieee754":">= 0.5 && < 0.9.7","buffer":true,"node:buffer":[">= 14.18 && < 15",">= 16"],"child_process":true,"node:child_process":[">= 14.18 && < 15",">= 16"],"cluster":">= 0.5","node:cluster":[">= 14.18 && < 15",">= 16"],"console":true,"node:console":[">= 14.18 && < 15",">= 16"],"constants":true,"node:constants":[">= 14.18 && < 15",">= 16"],"crypto":true,"node:crypto":[">= 14.18 && < 15",">= 16"],"_debug_agent":">= 1 && < 8","_debugger":"< 8","dgram":true,"node:dgram":[">= 14.18 && < 15",">= 16"],"diagnostics_channel":[">= 14.17 && < 15",">= 15.1"],"node:diagnostics_channel":[">= 14.18 && < 15",">= 16"],"dns":true,"node:dns":[">= 14.18 && < 15",">= 16"],"dns/promises":">= 15","node:dns/promises":">= 16","domain":">= 0.7.12","node:domain":[">= 14.18 && < 15",">= 16"],"events":true,"node:events":[">= 14.18 && < 15",">= 16"],"freelist":"< 6","fs":true,"node:fs":[">= 14.18 && < 15",">= 16"],"fs/promises":[">= 10 && < 10.1",">= 14"],"node:fs/promises":[">= 14.18 && < 15",">= 16"],"_http_agent":">= 0.11.1","node:_http_agent":[">= 14.18 && < 15",">= 16"],"_http_client":">= 0.11.1","node:_http_client":[">= 14.18 && < 15",">= 16"],"_http_common":">= 0.11.1","node:_http_common":[">= 14.18 && < 15",">= 16"],"_http_incoming":">= 0.11.1","node:_http_incoming":[">= 14.18 && < 15",">= 16"],"_http_outgoing":">= 0.11.1","node:_http_outgoing":[">= 14.18 && < 15",">= 16"],"_http_server":">= 0.11.1","node:_http_server":[">= 14.18 && < 15",">= 16"],"http":true,"node:http":[">= 14.18 && < 15",">= 16"],"http2":">= 8.8","node:http2":[">= 14.18 && < 15",">= 16"],"https":true,"node:https":[">= 14.18 && < 15",">= 16"],"inspector":">= 8","node:inspector":[">= 14.18 && < 15",">= 16"],"inspector/promises":[">= 19"],"node:inspector/promises":[">= 19"],"_linklist":"< 8","module":true,"node:module":[">= 14.18 && < 15",">= 16"],"net":true,"node:net":[">= 14.18 && < 15",">= 16"],"node-inspect/lib/_inspect":">= 7.6 && < 12","node-inspect/lib/internal/inspect_client":">= 7.6 && < 12","node-inspect/lib/internal/inspect_repl":">= 7.6 && < 12","os":true,"node:os":[">= 14.18 && < 15",">= 16"],"path":true,"node:path":[">= 14.18 && < 15",">= 16"],"path/posix":">= 15.3","node:path/posix":">= 16","path/win32":">= 15.3","node:path/win32":">= 16","perf_hooks":">= 8.5","node:perf_hooks":[">= 14.18 && < 15",">= 16"],"process":">= 1","node:process":[">= 14.18 && < 15",">= 16"],"punycode":">= 0.5","node:punycode":[">= 14.18 && < 15",">= 16"],"querystring":true,"node:querystring":[">= 14.18 && < 15",">= 16"],"readline":true,"node:readline":[">= 14.18 && < 15",">= 16"],"readline/promises":">= 17","node:readline/promises":">= 17","repl":true,"node:repl":[">= 14.18 && < 15",">= 16"],"node:sea":[">= 20.12 && < 21",">= 21.7"],"smalloc":">= 0.11.5 && < 3","_stream_duplex":">= 0.9.4","node:_stream_duplex":[">= 14.18 && < 15",">= 16"],"_stream_transform":">= 0.9.4","node:_stream_transform":[">= 14.18 && < 15",">= 16"],"_stream_wrap":">= 1.4.1","node:_stream_wrap":[">= 14.18 && < 15",">= 16"],"_stream_passthrough":">= 0.9.4","node:_stream_passthrough":[">= 14.18 && < 15",">= 16"],"_stream_readable":">= 0.9.4","node:_stream_readable":[">= 14.18 && < 15",">= 16"],"_stream_writable":">= 0.9.4","node:_stream_writable":[">= 14.18 && < 15",">= 16"],"stream":true,"node:stream":[">= 14.18 && < 15",">= 16"],"stream/consumers":">= 16.7","node:stream/consumers":">= 16.7","stream/promises":">= 15","node:stream/promises":">= 16","stream/web":">= 16.5","node:stream/web":">= 16.5","string_decoder":true,"node:string_decoder":[">= 14.18 && < 15",">= 16"],"sys":[">= 0.4 && < 0.7",">= 0.8"],"node:sys":[">= 14.18 && < 15",">= 16"],"test/reporters":">= 19.9 && < 20.2","node:test/reporters":[">= 18.17 && < 19",">= 19.9",">= 20"],"test/mock_loader":">= 22.3 && < 22.7","node:test/mock_loader":">= 22.3 && < 22.7","node:test":[">= 16.17 && < 17",">= 18"],"timers":true,"node:timers":[">= 14.18 && < 15",">= 16"],"timers/promises":">= 15","node:timers/promises":">= 16","_tls_common":">= 0.11.13","node:_tls_common":[">= 14.18 && < 15",">= 16"],"_tls_legacy":">= 0.11.3 && < 10","_tls_wrap":">= 0.11.3","node:_tls_wrap":[">= 14.18 && < 15",">= 16"],"tls":true,"node:tls":[">= 14.18 && < 15",">= 16"],"trace_events":">= 10","node:trace_events":[">= 14.18 && < 15",">= 16"],"tty":true,"node:tty":[">= 14.18 && < 15",">= 16"],"url":true,"node:url":[">= 14.18 && < 15",">= 16"],"util":true,"node:util":[">= 14.18 && < 15",">= 16"],"util/types":">= 15.3","node:util/types":">= 16","v8/tools/arguments":">= 10 && < 12","v8/tools/codemap":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/consarray":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/csvparser":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/logreader":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/profile_view":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8/tools/splaytree":[">= 4.4 && < 5",">= 5.2 && < 12"],"v8":">= 1","node:v8":[">= 14.18 && < 15",">= 16"],"vm":true,"node:vm":[">= 14.18 && < 15",">= 16"],"wasi":[">= 13.4 && < 13.5",">= 18.17 && < 19",">= 20"],"node:wasi":[">= 18.17 && < 19",">= 20"],"worker_threads":">= 11.7","node:worker_threads":[">= 14.18 && < 15",">= 16"],"zlib":">= 0.5","node:zlib":[">= 14.18 && < 15",">= 16"]}');

/***/ }),

/***/ "(instrument)/./node_modules/is-core-module/index.js":
/*!**********************************************!*\
  !*** ./node_modules/is-core-module/index.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar hasOwn = __webpack_require__(/*! hasown */ \"(instrument)/./node_modules/hasown/index.js\");\n\nfunction specifierIncluded(current, specifier) {\n\tvar nodeParts = current.split('.');\n\tvar parts = specifier.split(' ');\n\tvar op = parts.length > 1 ? parts[0] : '=';\n\tvar versionParts = (parts.length > 1 ? parts[1] : parts[0]).split('.');\n\n\tfor (var i = 0; i < 3; ++i) {\n\t\tvar cur = parseInt(nodeParts[i] || 0, 10);\n\t\tvar ver = parseInt(versionParts[i] || 0, 10);\n\t\tif (cur === ver) {\n\t\t\tcontinue; // eslint-disable-line no-restricted-syntax, no-continue\n\t\t}\n\t\tif (op === '<') {\n\t\t\treturn cur < ver;\n\t\t}\n\t\tif (op === '>=') {\n\t\t\treturn cur >= ver;\n\t\t}\n\t\treturn false;\n\t}\n\treturn op === '>=';\n}\n\nfunction matchesRange(current, range) {\n\tvar specifiers = range.split(/ ?&& ?/);\n\tif (specifiers.length === 0) {\n\t\treturn false;\n\t}\n\tfor (var i = 0; i < specifiers.length; ++i) {\n\t\tif (!specifierIncluded(current, specifiers[i])) {\n\t\t\treturn false;\n\t\t}\n\t}\n\treturn true;\n}\n\nfunction versionIncluded(nodeVersion, specifierValue) {\n\tif (typeof specifierValue === 'boolean') {\n\t\treturn specifierValue;\n\t}\n\n\tvar current = typeof nodeVersion === 'undefined'\n\t\t? process.versions && process.versions.node\n\t\t: nodeVersion;\n\n\tif (typeof current !== 'string') {\n\t\tthrow new TypeError(typeof nodeVersion === 'undefined' ? 'Unable to determine current node version' : 'If provided, a valid node version is required');\n\t}\n\n\tif (specifierValue && typeof specifierValue === 'object') {\n\t\tfor (var i = 0; i < specifierValue.length; ++i) {\n\t\t\tif (matchesRange(current, specifierValue[i])) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t\treturn false;\n\t}\n\treturn matchesRange(current, specifierValue);\n}\n\nvar data = __webpack_require__(/*! ./core.json */ \"(instrument)/./node_modules/is-core-module/core.json\");\n\nmodule.exports = function isCore(x, nodeVersion) {\n\treturn hasOwn(data, x) && versionIncluded(nodeVersion, data[x]);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/./node_modules/is-core-module/index.js\n");

/***/ })

};
;