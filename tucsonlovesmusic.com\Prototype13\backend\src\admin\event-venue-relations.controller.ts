import { Controller, Get, Post, Body, UseGuards } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { AuthGuard } from '../auth/auth.guard';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import * as sql from 'mssql';
import * as dotenv from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';

interface EventMissingVenue {
  id: string;
  name: string;
  date: string;
  azure_venue_id: string;
  azure_venue_name: string;
  postgres_venue_id: string | null;
  postgres_venue_name: string | null;
}

interface EventVenueStatusCounts {
  total: number;
  missingVenues: number;
  potentialFixes: number;
  mismatchedVenues: number;
}

class UpdateEventVenueDto {
  eventId: string;
  venueId: string;
}

@Controller('admin/event-venue-relations')
@UseGuards(AuthGuard)
export class EventVenueRelationsController {
  private azureConfig: sql.config;
  private azurePoolPromise: Promise<sql.ConnectionPool> | null = null;
  private connectionRetryAttempts = 3;
  private connectionRetryDelay = 3000; // 3 seconds
  
  constructor(@InjectDataSource() private dataSource: DataSource) {
    // Load environment variables
    dotenv.config();
    
    // Configure Azure SQL connection with best practices
    this.azureConfig = {
      server: process.env.AZURE_DB_SERVER || 'mssql.drv1.umbhost.net',
      database: process.env.AZURE_DB_NAME || 'TLM',
      user: process.env.AZURE_DB_USER || 'Reader',
      password: process.env.AZURE_DB_PASSWORD || 'TLM1234!',
      options: {
        encrypt: false,
        trustServerCertificate: true,
        enableArithAbort: true,
        connectTimeout: 30000, // 30 second timeout for connections
        requestTimeout: 30000  // 30 second timeout for requests
      },
      pool: {
        max: 10,           // Maximum number of connections
        min: 1,            // Minimum of 1 connection maintained
        idleTimeoutMillis: 30000,  // Close idle connections after 30 seconds
        acquireTimeoutMillis: 15000 // 15 seconds timeout when acquiring connection
      }
    };
  }
  
  /**
   * Get a connection to Azure SQL
   * This method uses connection pooling for better performance and implements
   * retry logic to handle transient connection issues
   */
  private async getAzurePool(): Promise<sql.ConnectionPool> {
    // If we already have a pool promise, verify its state before returning
    if (this.azurePoolPromise) {
      try {
        const pool = await this.azurePoolPromise;
        
        // Check if the pool is connected and functioning
        if (pool.connected) {
          // Verify connection with a simple query
          try {
            await pool.request().query('SELECT 1 AS testConnection');
            return pool; // Connection is working properly
          } catch (queryError) {
            console.warn('Azure SQL connection test failed:', 
              queryError instanceof Error ? queryError.message : 'Unknown error');
            // Connection is not usable despite connected=true, create a new one
            await this.closeExistingPool(); 
          }
        } else {
          // Pool reports it's not connected, create a new one
          await this.closeExistingPool();
        }
      } catch (error) {
        // Error with existing pool, log and create a new one
        console.error('Azure SQL pool access error:', 
          error instanceof Error ? error.message : 'Unknown error');
        await this.closeExistingPool();
      }
    }
    
    // Create a new pool with retry logic
    return this.createNewPool();
  }
  
  /**
   * Safely close the existing pool and reset the promise
   */
  private async closeExistingPool(): Promise<void> {
    if (this.azurePoolPromise) {
      try {
        const pool = await this.azurePoolPromise;
        if (pool && pool.connected) {
          await pool.close();
        }
      } catch (error) {
        console.warn('Error closing Azure SQL pool:', 
          error instanceof Error ? error.message : 'Unknown error');
      } finally {
        this.azurePoolPromise = null;
      }
    }
  }
  
  /**
   * Create a new connection pool with retry logic
   */
  private async createNewPool(): Promise<sql.ConnectionPool> {
    let retryCount = 0;
    let lastError: any = null;
    
    while (retryCount < this.connectionRetryAttempts) {
      try {
        // Create and connect to a new pool
        this.azurePoolPromise = new sql.ConnectionPool(this.azureConfig).connect();
        const pool = await this.azurePoolPromise;
        
        // Test the connection
        await pool.request().query('SELECT 1 AS testConnection');
        
        console.log(`Successfully connected to Azure SQL (attempt ${retryCount + 1})`);
        return pool;
      } catch (error) {
        lastError = error;
        retryCount++;
        
        console.warn(`Azure SQL connection attempt ${retryCount} failed:`, 
          error instanceof Error ? error.message : 'Unknown error');
        
        // Reset the promise for the next attempt
        this.azurePoolPromise = null;
        
        // Wait before retrying, but only if we're going to retry again
        if (retryCount < this.connectionRetryAttempts) {
          await new Promise(resolve => setTimeout(resolve, this.connectionRetryDelay));
        }
      }
    }
    
    // If we've exhausted all retries, throw the last error
    console.error('All Azure SQL connection attempts failed');
    throw lastError;
  }

  /**
   * Get status counts for event-venue relationships
   * Connects to both PostgreSQL and Azure SQL to compare relationships
   */
  @Get()
  async getEventVenueStatusCounts(): Promise<EventVenueStatusCounts> {
    let azurePool = null;
    
    try {
      // Connect to Azure SQL with improved error handling
      azurePool = await this.getAzurePool();
      
      // Get total events count in PostgreSQL
      const totalEventsResult = await this.dataSource.query(`
        SELECT COUNT(*) as total FROM event WHERE deleted = false
      `);
      const totalEvents = parseInt(totalEventsResult[0].total);
      
      // Get events in PostgreSQL with Azure IDs but no venue relationship
      const missingVenuesResult = await this.dataSource.query(`
        SELECT COUNT(DISTINCT e.id) as count 
        FROM event e
        WHERE e.deleted = false
        AND e.azure_id IS NOT NULL
        AND (e.venue_id IS NULL)
      `);
      const missingVenues = parseInt(missingVenuesResult[0].count);
      
      // Get events that have a venue but may be incorrect (mismatched)
      // For the status counts, we'll just count events with venues for now
      // The actual mismatches will be detected when fetching the detailed list
      // This avoids UUID comparison issues in this query
      const mismatchedResult = await this.dataSource.query(`
        SELECT COUNT(DISTINCT e.id) as count
        FROM event e
        JOIN venue v ON e.venue_id = v.id
        WHERE e.deleted = false 
        AND e.azure_id IS NOT NULL 
      `);
      const mismatchedVenues = parseInt(mismatchedResult[0].count);
      
      // Get events that can potentially be fixed (have Azure IDs)
      const potentialFixesResult = await this.dataSource.query(`
        SELECT COUNT(*) as count 
        FROM event e
        WHERE e.deleted = false
        AND e.azure_id IS NOT NULL
        AND e.azure_id != ''
      `);
      const potentialFixes = parseInt(potentialFixesResult[0].count);
      
      return {
        total: totalEvents,
        missingVenues,
        mismatchedVenues,
        potentialFixes
      };
    } catch (error) {
      console.error('Error in getEventVenueStatusCounts:', error instanceof Error ? error.message : 'Unknown error');
      if (error instanceof Error && error.message.includes('Connection is closed')) {
        console.warn('Connection issue detected, will retry on next request');
        // Reset the pool to force a new connection on next request
        await this.closeExistingPool();
      }
      throw error;
    }
  }

  /**
   * Get events with missing venue relationships
   * Connects to both PostgreSQL and Azure SQL to find events that have venues in Azure but not in Postgres
   */
  @Get('missing')
  async getEventsWithMissingVenues(): Promise<EventMissingVenue[]> {
    let azurePool = null;
    
    try {
      // Connect to Azure SQL with improved error handling
      azurePool = await this.getAzurePool();
      
      // First, get events from PostgreSQL with no venue or venue_id
      const query = `
        SELECT 
          e.id, 
          e.name, 
          e.azure_id,
          e."startDateTime" as date,
          e.venue_id as postgres_venue_id,
          v.name as postgres_venue_name
        FROM event e
        LEFT JOIN venue v ON e.venue_id = v.id
        WHERE e.deleted = false
        AND e.azure_id IS NOT NULL
        AND (e.venue_id IS NULL)
        ORDER BY e."startDateTime" DESC
        LIMIT 100
      `;
      
      const events = await this.dataSource.query(query);
      
      // For each event, check Azure SQL to see if it has a venue
      const results = [];
      
      for (const event of events) {
        // Get venue for this event from Azure
        const azureResult = await azurePool.request()
          .input('eventId', sql.UniqueIdentifier, event.azure_id)
          .query(`
            SELECT v.Id as venueId, v.Name as venueName
            FROM PerformanceEvents pe
            JOIN Venues v ON pe.VenueId = v.Id
            WHERE pe.Id = @eventId
          `);
        
        if (azureResult.recordset.length > 0) {
          const azureVenue = azureResult.recordset[0];
          results.push({
            id: event.id,
            name: event.name,
            date: event.date,
            azure_venue_id: azureVenue.venueId,
            azure_venue_name: azureVenue.venueName,
            postgres_venue_id: event.postgres_venue_id,
            postgres_venue_name: event.postgres_venue_name
          });
        }
      }
      
      return results;
    } catch (error) {
      console.error('Error in getEventsWithMissingVenues:', error instanceof Error ? error.message : 'Unknown error');
      if (error instanceof Error && error.message.includes('Connection is closed')) {
        console.warn('Connection issue detected, will retry on next request');
        // Reset the pool to force a new connection on next request
        await this.closeExistingPool();
      }
      throw error;
    }
  }

  /**
   * Get events with mismatched venue relationships
   * Finds events where the PostgreSQL and Azure venue IDs don't match
   */
  @Get('mismatched')
  async getEventsWithMismatchedVenues(): Promise<EventMissingVenue[]> {
    let azurePool = null;
    
    try {
      // Connect to Azure SQL with improved error handling
      azurePool = await this.getAzurePool();
      
      // First, get events from PostgreSQL that have a venue
      const query = `
        SELECT 
          e.id, 
          e.name, 
          e.azure_id,
          e."startDateTime" as date,
          e.venue_id as postgres_venue_id,
          v.name as postgres_venue_name,
          v.azure_id as postgres_venue_azure_id
        FROM event e
        JOIN venue v ON e.venue_id = v.id
        WHERE e.deleted = false
        AND e.azure_id IS NOT NULL
        ORDER BY e."startDateTime" DESC
        LIMIT 100
      `;
      
      const events = await this.dataSource.query(query);
      const results = [];
      
      // For each event, check Azure SQL to see if the venues match
      for (const event of events) {
        // Get venue for this event from Azure
        const azureResult = await azurePool.request()
          .input('eventId', sql.UniqueIdentifier, event.azure_id)
          .query(`
            SELECT v.Id as venueId, v.Name as venueName
            FROM PerformanceEvents pe
            JOIN Venues v ON pe.VenueId = v.Id
            WHERE pe.Id = @eventId
          `);
        
        if (azureResult.recordset.length > 0) {
          const azureVenue = azureResult.recordset[0];
          
          // Check if the venue IDs match (accounting for case sensitivity)
          const azureVenueId = azureVenue.venueId.toLowerCase();
          const postgresVenueAzureId = event.postgres_venue_azure_id ? event.postgres_venue_azure_id.toLowerCase() : null;
          
          // Compare venue names (normalized for comparison)
          const azureVenueName = azureVenue.venueName.trim().toLowerCase();
          const postgresVenueName = event.postgres_venue_name ? event.postgres_venue_name.trim().toLowerCase() : '';
          
          // Only consider truly mismatched venues where:  
          // 1. The venue IDs don't match AND
          // 2. The venue names don't match either
          if ((!postgresVenueAzureId || azureVenueId !== postgresVenueAzureId) && 
              azureVenueName !== postgresVenueName) {
            results.push({
              id: event.id,
              name: event.name,
              date: event.date,
              azure_venue_id: azureVenue.venueId,
              azure_venue_name: azureVenue.venueName,
              postgres_venue_id: event.postgres_venue_id,
              postgres_venue_name: event.postgres_venue_name
            });
          }
        }
      }
      
      return results;
    } catch (error) {
      console.error('Error in getEventsWithMismatchedVenues:', error instanceof Error ? error.message : 'Unknown error');
      if (error instanceof Error && error.message.includes('Connection is closed')) {
        console.warn('Connection issue detected, will retry on next request');
        // Reset the pool to force a new connection on next request
        await this.closeExistingPool();
      }
      throw error;
    }
  }

  @Post('fix')
  async fixEventVenueRelationship(@Body() updateDto: UpdateEventVenueDto) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const { eventId, venueId } = updateDto;
      let azurePool = null;
      
      // Connect to Azure SQL
      azurePool = await this.getAzurePool();

      // Get the venue info from Azure SQL
      const azureVenueResult = await azurePool.request()
        .input('venueId', sql.UniqueIdentifier, venueId)
        .query(`
          SELECT Id, Name 
          FROM Venues 
          WHERE Id = @venueId
        `);

      if (azureVenueResult.recordset.length === 0) {
        throw new Error(`Venue with ID ${venueId} not found in Azure SQL`);
      }

      const azureVenue = azureVenueResult.recordset[0];

      // Check if this venue already exists in PostgreSQL
      const existingVenueQuery = `
        SELECT id 
        FROM venue 
        WHERE azure_id = $1
      `;
      const existingVenueResult = await queryRunner.query(existingVenueQuery, [venueId]);
      
      let postgresVenueId;

      // If venue doesn't exist in PostgreSQL, create it
      if (existingVenueResult.length === 0) {
        // Generate a slug from the venue name
        const generateSlug = (name: string): string => {
          return name
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-') // Replace non-alphanumeric with hyphens
            .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
            .substring(0, 100) // Limit length
            + '-' + Date.now().toString().slice(-6); // Add timestamp suffix to ensure uniqueness
        };
        
        const venueSlug = generateSlug(azureVenue.Name);
        
        const insertVenueQuery = `
          INSERT INTO venue (name, azure_id, slug, created_at, updated_at) 
          VALUES ($1, $2, $3, NOW(), NOW()) 
          RETURNING id
        `;
        const newVenueResult = await queryRunner.query(insertVenueQuery, [
          azureVenue.Name,
          azureVenue.Id,
          venueSlug
        ]);
        postgresVenueId = newVenueResult[0].id;
      } else {
        postgresVenueId = existingVenueResult[0].id;
      }

      // Update the event with the venue ID
      const updateEventQuery = `
        UPDATE event 
        SET venue_id = $1, updated_at = NOW() 
        WHERE id = $2
      `;
      await queryRunner.query(updateEventQuery, [postgresVenueId, eventId]);

      // Commit the transaction
      await queryRunner.commitTransaction();

      return { success: true, message: 'Event venue relationship fixed successfully' };
    } catch (error) {
      // Rollback in case of error
      await queryRunner.rollbackTransaction();
      console.error('Error fixing event venue relationship:', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Bulk fix all event-venue relationships
   * This endpoint will synchronize venue relationships from Azure to PostgreSQL
   */
  @Post('fix-all')
  async fixAllEventVenueRelationships() {
    let azurePool = null;
    let fixedCount = 0;
    let errorCount = 0;
    
    try {
      // Connect to Azure SQL
      azurePool = await this.getAzurePool();
      
      // Get all events from PostgreSQL that have Azure IDs but missing venue relationships
      const query = `
        SELECT 
          e.id, 
          e.azure_id
        FROM event e
        WHERE e.deleted = false
        AND e.azure_id IS NOT NULL
        AND (e.venue_id IS NULL OR e.venue_id = '')
        ORDER BY e."startDateTime" DESC
        LIMIT 500
      `;
      
      const events = await this.dataSource.query(query);
      
      // Process each event
      for (const event of events) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        
        try {
          // Get venue for this event from Azure
          const azureResult = await azurePool.request()
            .input('eventId', sql.UniqueIdentifier, event.azure_id)
            .query(`
              SELECT v.Id as venueId, v.Name as venueName
              FROM PerformanceEvents pe
              JOIN Venues v ON pe.VenueId = v.Id
              WHERE pe.Id = @eventId
            `);
          
          if (azureResult.recordset.length > 0) {
            const azureVenue = azureResult.recordset[0];
            
            // Check if venue exists in PostgreSQL
            const existingVenueQuery = `
              SELECT id 
              FROM venue 
              WHERE azure_id = $1
            `;
            const existingVenueResult = await queryRunner.query(existingVenueQuery, [azureVenue.venueId]);
            
            let postgresVenueId;
            
            // If venue doesn't exist in PostgreSQL, create it
            if (existingVenueResult.length === 0) {
              // Generate a slug from the venue name
              const generateSlug = (name: string): string => {
                return name
                  .toLowerCase()
                  .replace(/[^a-z0-9]+/g, '-') // Replace non-alphanumeric with hyphens
                  .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
                  .substring(0, 100) // Limit length
                  + '-' + Date.now().toString().slice(-6); // Add timestamp suffix to ensure uniqueness
              };
              
              const venueSlug = generateSlug(azureVenue.venueName);
              
              const insertVenueQuery = `
                INSERT INTO venue (name, azure_id, slug, created_at, updated_at) 
                VALUES ($1, $2, $3, NOW(), NOW()) 
                RETURNING id
              `;
              const newVenueResult = await queryRunner.query(insertVenueQuery, [
                azureVenue.venueName,
                azureVenue.venueId,
                venueSlug
              ]);
              postgresVenueId = newVenueResult[0].id;
            } else {
              postgresVenueId = existingVenueResult[0].id;
            }
            
            // Update the event with the venue ID
            const updateEventQuery = `
              UPDATE event 
              SET venue_id = $1, updated_at = NOW() 
              WHERE id = $2
            `;
            await queryRunner.query(updateEventQuery, [postgresVenueId, event.id]);
            
            // Commit the transaction
            await queryRunner.commitTransaction();
            fixedCount++;
          }
        } catch (error) {
          // Rollback in case of error
          await queryRunner.rollbackTransaction();
          console.error(`Error fixing event ${event.id}:`, error instanceof Error ? error.message : 'Unknown error');
          errorCount++;
        } finally {
          // Release the query runner
          await queryRunner.release();
        }
      }
      
      return {
        success: true,
        message: `Fixed ${fixedCount} event-venue relationships${errorCount > 0 ? ` (${errorCount} errors)` : ''}`,
        fixed: fixedCount,
        errors: errorCount
      };
    } catch (error) {
      console.error('Error in bulk fix operation:', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }

  @Post('fix-from-csv')
  @ApiOperation({ summary: 'Fix event-venue relationships using CSV data with Azure MSSQL validation' })
  @ApiResponse({ status: 200, description: 'Event-venue relationships fixed successfully' })
  async fixEventVenueRelationshipsFromCsv(): Promise<any> {
    console.log('🔧 [Backend] Starting CSV-based event-venue relationship fix with Azure validation...');

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    let transactionStarted = false;
    let azurePool = null;

    try {
      // 1. Start transaction
      await queryRunner.startTransaction();
      transactionStarted = true;
      console.log('✅ [Backend] Transaction started');

      console.log('🔗 [Backend] Connecting to Azure MSSQL for validation...');
      azurePool = await this.getAzurePool();
      console.log('✅ [Backend] Connected to Azure MSSQL successfully');

      // 2. Read and parse CSV file correctly
      const csvPath = path.join(process.cwd(), 'data/imports/venuejoins.csv');
      console.log('📁 [Backend] Reading CSV from:', csvPath);

      const csvContent = fs.readFileSync(csvPath, 'utf-8');
      const lines = csvContent.trim().split('\n');
      const header = lines[0];
      const dataLines = lines.slice(1);

      console.log(`📊 [Backend] CSV contains ${dataLines.length} relationships`);

      // 3. Parse CSV relationships as GUIDs
      const csvRelationships = dataLines.map((line, index) => {
        const [eventGuid, venueGuid] = line.split(',').map(id => id.trim());

        // Validate GUID format
        const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        if (!guidRegex.test(eventGuid) || !guidRegex.test(venueGuid)) {
          console.warn(`⚠️ [Backend] Invalid GUID format at line ${index + 2}: Event=${eventGuid}, Venue=${venueGuid}`);
          return null;
        }

        return {
          eventGuid: eventGuid.toLowerCase(),
          venueGuid: venueGuid.toLowerCase()
        };
      }).filter(rel => rel !== null);

      console.log(`✅ [Backend] Parsed ${csvRelationships.length} valid GUID relationships`);

      // 4. Validate relationships against Azure MSSQL source
      console.log('🔍 [Backend] Validating relationships against Azure MSSQL source...');
      const validatedRelationships = [];
      let invalidEventCount = 0;
      let invalidVenueCount = 0;
      let validRelationshipCount = 0;

      // PERFORMANCE OPTIMIZATION: Batch validation instead of individual queries
      // Get all unique event and venue IDs from CSV
      const uniqueEventIds = [...new Set(csvRelationships.map(r => r.eventGuid))];
      const uniqueVenueIds = [...new Set(csvRelationships.map(r => r.venueGuid))];

      console.log(`📊 [Backend] Validating ${uniqueEventIds.length} unique events and ${uniqueVenueIds.length} unique venues...`);

      // Batch validate events
      const validEventIds = new Set<string>();
      const eventBatchSize = 100;
      for (let i = 0; i < uniqueEventIds.length; i += eventBatchSize) {
        const batch = uniqueEventIds.slice(i, i + eventBatchSize);
        const placeholders = batch.map((_, index) => `@eventId${index}`).join(',');

        const request = azurePool.request();
        batch.forEach((eventId, index) => {
          request.input(`eventId${index}`, sql.UniqueIdentifier, eventId);
        });

        const eventResult = await request.query(`
          SELECT Id FROM PerformanceEvents WHERE Id IN (${placeholders})
        `);

        eventResult.recordset.forEach(row => {
          validEventIds.add(row.Id.toLowerCase());
        });
      }

      // Batch validate venues
      const validVenueIds = new Set<string>();
      const venueBatchSize = 100;
      for (let i = 0; i < uniqueVenueIds.length; i += venueBatchSize) {
        const batch = uniqueVenueIds.slice(i, i + venueBatchSize);
        const placeholders = batch.map((_, index) => `@venueId${index}`).join(',');

        const request = azurePool.request();
        batch.forEach((venueId, index) => {
          request.input(`venueId${index}`, sql.UniqueIdentifier, venueId);
        });

        const venueResult = await request.query(`
          SELECT Id FROM Venues WHERE Id IN (${placeholders})
        `);

        venueResult.recordset.forEach(row => {
          validVenueIds.add(row.Id.toLowerCase());
        });
      }

      // Batch validate actual relationships
      const validRelationshipKeys = new Set<string>();
      const relationshipBatchSize = 50;
      for (let i = 0; i < csvRelationships.length; i += relationshipBatchSize) {
        const batch = csvRelationships.slice(i, i + relationshipBatchSize);

        const conditions = batch.map((_, index) =>
          `(pe.Id = @eventId${index} AND pe.VenueId = @venueId${index})`
        ).join(' OR ');

        const request = azurePool.request();
        batch.forEach((rel, index) => {
          request.input(`eventId${index}`, sql.UniqueIdentifier, rel.eventGuid);
          request.input(`venueId${index}`, sql.UniqueIdentifier, rel.venueGuid);
        });

        const relationshipResult = await request.query(`
          SELECT pe.Id as EventId, pe.VenueId
          FROM PerformanceEvents pe
          WHERE ${conditions}
        `);

        relationshipResult.recordset.forEach(row => {
          const key = `${row.EventId.toLowerCase()}-${row.VenueId.toLowerCase()}`;
          validRelationshipKeys.add(key);
        });
      }

      console.log(`✅ [Backend] Batch validation complete - found ${validEventIds.size} valid events, ${validVenueIds.size} valid venues, ${validRelationshipKeys.size} valid relationships`);

      // Now validate each relationship using the cached results
      for (const relationship of csvRelationships) {
        const eventId = relationship.eventGuid.toLowerCase();
        const venueId = relationship.venueGuid.toLowerCase();
        const relationshipKey = `${eventId}-${venueId}`;

        if (!validEventIds.has(eventId)) {
          invalidEventCount++;
          continue;
        }

        if (!validVenueIds.has(venueId)) {
          invalidVenueCount++;
          continue;
        }

        if (!validRelationshipKeys.has(relationshipKey)) {
          continue;
        }

        validatedRelationships.push(relationship);
        validRelationshipCount++;
      }

      console.log(`📊 [Backend] Validation summary:`);
      console.log(`   - Valid relationships: ${validRelationshipCount}`);
      console.log(`   - Invalid events: ${invalidEventCount}`);
      console.log(`   - Invalid venues: ${invalidVenueCount}`);

      return {
        success: true,
        message: `CSV validation completed successfully`,
        stats: {
          totalCsvRelationships: csvRelationships.length,
          validatedRelationships: validRelationshipCount,
          invalidEvents: invalidEventCount,
          invalidVenues: invalidVenueCount,
          validEvents: validEventIds.size,
          validVenues: validVenueIds.size
        }
      };

    } catch (error) {
      console.error('🔧 [Backend] Error in CSV-based venue relationship fix:', error);

      if (transactionStarted) {
        try {
          await queryRunner.rollbackTransaction();
          console.log('🔄 [Backend] Transaction rolled back due to error');
        } catch (rollbackError) {
          console.error('🔧 [Backend] Error rolling back transaction:', rollbackError);
        }
      }

      throw error;
    } finally {
      // Clean up resources
      if (queryRunner) {
        try {
          await queryRunner.release();
          console.log('🔄 [Backend] Query runner released');
        } catch (releaseError) {
          console.error('🔧 [Backend] Error releasing query runner:', releaseError);
        }
      }
    }
  }
}