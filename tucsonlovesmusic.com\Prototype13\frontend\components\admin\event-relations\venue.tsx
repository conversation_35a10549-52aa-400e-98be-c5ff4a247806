"use client";

import { useEffect, useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import {
  GitMerge,
  Loader2,
  AlertCircle,
  Calendar,
  ChevronDown,
  ChevronRight,
  Check,
  Wrench,
  RefreshCw
} from "lucide-react";
import { useAuth } from "@/hooks/use-auth";
import { 
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { getApiUrl } from "@/lib/config";

interface EventMissingVenue {
  id: string;
  name: string;
  date: string;
  azure_venue_id: string;
  azure_venue_name: string;
  postgres_venue_id: string | null;
  postgres_venue_name: string | null;
}

interface EventVenueStatusCounts {
  total: number;
  missingVenues: number;
  potentialFixes: number;
  mismatchedVenues: number;
}

export function EventVenueRelations() {
  const [counts, setCounts] = useState<EventVenueStatusCounts | null>(null);
  const [missingEvents, setMissingEvents] = useState<EventMissingVenue[]>([]);
  const [mismatchedEvents, setMismatchedEvents] = useState<EventMissingVenue[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [batchFixing, setBatchFixing] = useState(false);
  const [csvFixing, setCsvFixing] = useState(false);
  const [batchFixProgress, setBatchFixProgress] = useState({ current: 0, total: 0, success: 0, failed: 0 });
  const [fixingEvents, setFixingEvents] = useState<Record<string, boolean>>({});
  const [error, setError] = useState<string | null>(null);
  const [expanded, setExpanded] = useState<{ missing: boolean; mismatched: boolean }>({
    missing: false,
    mismatched: false
  });
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<EventMissingVenue | null>(null);
  const { getAccessToken } = useAuth();
  const { toast } = useToast();

  // Fetch data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      const token = await getAccessToken();
      const baseUrl = getApiUrl();
      
      // Fetch counts
      const countsResponse = await fetch(`${baseUrl}/admin/event-venue-relations`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!countsResponse.ok) {
        throw new Error('Failed to fetch event-venue relationship counts');
      }

      const countsData = await countsResponse.json();
      setCounts(countsData);

      // Fetch missing events
      const missingResponse = await fetch(`${baseUrl}/admin/event-venue-relations/missing`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (missingResponse.ok) {
        const missingData = await missingResponse.json();
        setMissingEvents(missingData);
      }

      // Fetch mismatched events
      const mismatchedResponse = await fetch(`${baseUrl}/admin/event-venue-relations/mismatched`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (mismatchedResponse.ok) {
        const mismatchedData = await mismatchedResponse.json();
        setMismatchedEvents(mismatchedData);
      }
      
      setError(null);
    } catch (err: unknown) {
      console.error('Error fetching event-venue relationship data', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch event-venue relationship data');
    } finally {
      setIsLoading(false);
    }
  };

  const toggleExpanded = (section: 'missing' | 'mismatched') => {
    setExpanded(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };



  // Handle batch fixing a limited number of events
  const handleBatchFix = async () => {
    try {
      setBatchFixing(true);
      const token = await getAccessToken();
      const baseUrl = getApiUrl();
      
      // Determine which events to fix - prioritize missing events
      const eventsToFix = [...missingEvents].slice(0, 10); // Limit to 10 events at a time
      
      if (eventsToFix.length === 0) {
        toast({
          title: 'No Events to Fix',
          description: 'There are no events that need fixing.',
          variant: 'default'
        });
        return;
      }
      
      // Set up progress tracking
      setBatchFixProgress({
        current: 0,
        total: eventsToFix.length,
        success: 0,
        failed: 0
      });
      
      // Mark all events as being fixed
      const newFixingState: Record<string, boolean> = {};
      eventsToFix.forEach(event => {
        newFixingState[event.id] = true;
      });
      setFixingEvents(prev => ({ ...prev, ...newFixingState }));
      
      console.log(`🔧 [Frontend] Starting batch fix for ${eventsToFix.length} venue relationships`);

      // Track success and failure counts locally to avoid state update timing issues
      let successCount = 0;
      let failureCount = 0;

      // Process events sequentially to avoid overwhelming the server
      for (let i = 0; i < eventsToFix.length; i++) {
        const event = eventsToFix[i];
        setBatchFixProgress(prev => ({ ...prev, current: i + 1 }));

        try {
          console.log(`🔧 [Frontend] Fixing event ${i + 1}/${eventsToFix.length}: ${event.name} (ID: ${event.id})`);

          // Create the request body with eventId and venueId
          const requestBody = {
            eventId: event.id,
            venueId: event.azure_venue_id // Using the Azure venue ID from the selected event
          };

          const response = await fetch(`${baseUrl}/admin/event-venue-relations/fix`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
          });

          if (!response.ok) {
            console.error(`🔧 [Frontend] Failed to fix event ${event.name}: ${response.status}`);
            failureCount++;
            setBatchFixProgress(prev => ({ ...prev, failed: prev.failed + 1 }));
          } else {
            console.log(`🔧 [Frontend] Successfully fixed event ${event.name}`);
            successCount++;
            setBatchFixProgress(prev => ({ ...prev, success: prev.success + 1 }));
          }
        } catch (eventError) {
          console.error(`🔧 [Frontend] Error fixing event ${event.name}:`, eventError);
          failureCount++;
          setBatchFixProgress(prev => ({ ...prev, failed: prev.failed + 1 }));
        } finally {
          // Mark this event as no longer being fixed
          setFixingEvents(prev => ({ ...prev, [event.id]: false }));
        }

        // Small delay to avoid overwhelming the server
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      toast({
        title: 'Batch Fix Complete',
        description: `Successfully fixed ${successCount} out of ${eventsToFix.length} venue relationships${failureCount > 0 ? ` (${failureCount} failed)` : ''}.`,
        variant: successCount > 0 ? 'default' : 'destructive'
      });

      // Refresh data
      fetchData();
    } catch (err: unknown) {
      console.error('Error during batch fix operation', err);

      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to complete batch fix operation',
        variant: 'destructive'
      });
    } finally {
      setBatchFixing(false);
      // Reset progress
      setBatchFixProgress({ current: 0, total: 0, success: 0, failed: 0 });
    }
  };

  // Handle CSV-based fix for all event-venue relationships
  const handleCsvFix = async () => {
    try {
      setCsvFixing(true);
      const token = await getAccessToken();
      const baseUrl = getApiUrl();

      console.log('🔧 [Frontend] Starting CSV-based fix for event-venue relationships');

      const response = await fetch(`${baseUrl}/admin/event-venue-relations/fix-from-csv`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('🔧 [Frontend] Server error response:', errorText);
        throw new Error(`Failed to execute CSV fix: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('🔧 [Frontend] CSV fix completed:', result);

      toast({
        title: 'CSV Fix Complete',
        description: `Validated ${result.stats?.validatedRelationships || 0} venue relationships from CSV`,
        variant: 'default'
      });

      // Refresh data after CSV fix
      fetchData();
    } catch (err: unknown) {
      console.error('🔧 [Frontend] Error during CSV fix operation', err);

      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to complete CSV fix operation',
        variant: 'destructive'
      });
    } finally {
      setCsvFixing(false);
    }
  };

  // Open confirmation dialog for fixing an event
  const openFixConfirmDialog = (event: EventMissingVenue) => {
    setSelectedEvent(event);
    setConfirmDialogOpen(true);
  };

  // Handle fixing a single event's venue relationship through the new confirmation dialog
  const handleFixEventVenue = async () => {
    if (!selectedEvent) return;
    
    try {
      // Close the dialog
      setConfirmDialogOpen(false);
      
      // Mark this event as being fixed
      setFixingEvents(prev => ({ ...prev, [selectedEvent.id]: true }));
      
      const token = await getAccessToken();
      const baseUrl = getApiUrl();
      
      // Create the request body with eventId and venueId
      const requestBody = {
        eventId: selectedEvent.id,
        venueId: selectedEvent.azure_venue_id // Using the Azure venue ID from the selected event
      };
      
      const response = await fetch(`${baseUrl}/admin/event-venue-relations/fix`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fix event-venue relationship for ${selectedEvent.name}`);
      }
      
      await response.json();

      toast({
        title: 'Success',
        description: `Event "${selectedEvent?.name}" venue updated to "${selectedEvent?.azure_venue_name}"`,
        variant: 'default'
      });
      
      // Refresh data after successful fix
      fetchData();
    } catch (err: unknown) {
      console.error('Error fixing event-venue relationship', err);
      
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : `Failed to fix event-venue relationship for ${selectedEvent?.name}`,
        variant: 'destructive'
      });
    } finally {
      // Clear the fixing state for this event
      if (selectedEvent) {
        setFixingEvents(prev => ({ ...prev, [selectedEvent.id]: false }));
      }
      // Clear the selected event
      setSelectedEvent(null);
    }
  };


  // Determine status message and icon color
  const getStatusDetails = () => {
    if (!counts) return { message: 'Loading...', status: 'neutral' };
    
    // Check if there are any actual problematic relationships in the dropdown lists
    const hasProblematicRelations = missingEvents.length > 0 || mismatchedEvents.length > 0;
    
    // If there are no problematic relations to troubleshoot, show green
    // Only show yellow warning when there's something that needs attention
    if (!hasProblematicRelations) {
      return { 
        message: counts.missingVenues === 0
          ? 'All events have venue relationships' 
          : `${counts.missingVenues} events with no venue relations`, 
        status: 'good' 
      };
    }
    
    // Show yellow warning when there are items in the dropdown lists that need attention
    return {
      message: `${counts.missingVenues} events with no venue relations`,
      status: 'warning'
    };
  };
  
  // Get the appropriate icon color based on status
  const getIconColorClass = (status: string) => {
    switch (status) {
      case 'good': return 'text-green-500';
      case 'warning': return 'text-yellow-500';
      case 'bad': return 'text-red-500';
      default: return 'text-muted-foreground';
    }
  };
  
  const statusDetails = getStatusDetails();
  
  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return 'No date';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center space-y-0 pb-2">
        <GitMerge className={`h-5 w-5 mr-2 ${getIconColorClass(statusDetails.status)}`} />
        <div>
          <CardTitle className="text-xl">Events - Venues</CardTitle>
          {/* <p className="text-sm text-muted-foreground">Manage relationships between events and venues</p> */}
        </div>
      </CardHeader>
      
      <CardContent>
        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <p className="mt-2 text-sm text-muted-foreground">Loading relationship data...</p>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center py-8 px-4">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
            <p className="text-sm text-red-500">{error}</p>
          </div>
        ) : (
          <>
            <div className="flex flex-col space-y-4">
              {/* Status indicator */}
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <p className="text-sm">{statusDetails.message}</p>
              </div>
              
              {/* Action buttons */}
              {/* Commented out as requested - global fixes would take too long for UI operations
              <div className="flex space-x-2">
                <Button 
                  onClick={handleFixAll} 
                  disabled={isBulkFixing || counts?.missingVenues === 0}
                  className="flex items-center"
                >
                  {isBulkFixing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Fixing...
                    </>
                  ) : (
                    <>
                      <Zap className="mr-2 h-4 w-4" />
                      Fix All Relationships
                    </>
                  )}
                </Button>
                
                <Button 
                  variant="outline" 
                  onClick={fetchData} 
                  disabled={isLoading || isBulkFixing}
                >
                  Refresh
                </Button>
              </div>
              */}
              
              {/* Batch Fix, CSV Fix, and Refresh buttons */}
              <div className="flex space-x-2">
                {/* Batch Fix button - fixes up to 10 events at a time */}
                <Button
                  onClick={handleBatchFix}
                  disabled={batchFixing || csvFixing || missingEvents.length === 0}
                  className="flex items-center"
                  variant="outline"
                >
                  {batchFixing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Fixing {batchFixProgress.current}/{batchFixProgress.total}...
                    </>
                  ) : (
                    <>
                      <Wrench className="mr-2 h-4 w-4" />
                      Batch Fix (10 Events)
                    </>
                  )}
                </Button>

                {/* CSV Fix button - validates all relationships from CSV */}
                <Button
                  onClick={handleCsvFix}
                  disabled={batchFixing || csvFixing}
                  className="flex items-center"
                  variant="outline"
                >
                  {csvFixing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Validating CSV...
                    </>
                  ) : (
                    <>
                      <Check className="mr-2 h-4 w-4" />
                      Validate from CSV
                    </>
                  )}
                </Button>

                {/* Refresh button */}
                <Button
                  variant="outline"
                  size="icon"
                  onClick={fetchData}
                  disabled={isLoading || batchFixing || csvFixing}
                  title="Refresh data"
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
              
              {/* Batch fix progress indicator */}
              {batchFixing && (
                <div className="mt-2 p-2 bg-muted rounded-md">
                  <div className="text-sm mb-1 flex justify-between">
                    <span>Batch Fix Progress</span>
                    <span>{batchFixProgress.current}/{batchFixProgress.total} events</span>
                  </div>
                  <div className="w-full bg-secondary h-2 rounded-full overflow-hidden">
                    <div 
                      className="bg-primary h-full transition-all duration-300 ease-in-out" 
                      style={{ width: `${(batchFixProgress.current / Math.max(batchFixProgress.total, 1)) * 100}%` }}
                    />
                  </div>
                  <div className="flex justify-between text-xs mt-1">
                    <span className="text-green-500">{batchFixProgress.success} successful</span>
                    <span className="text-red-500">{batchFixProgress.failed} failed</span>
                  </div>
                </div>
              )}
              
              {/* Event sections */}
              <div className="space-y-4">
                {/* Missing Venues section */}
                <Collapsible 
                  open={expanded.missing} 
                  onOpenChange={() => toggleExpanded('missing')}
                  className="border rounded-md"
                >
                  <CollapsibleTrigger className="flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors">
                    <div className="flex items-center">
                      <Calendar className="mr-2 h-4 w-4" />
                      <span>Events Missing Venue Relationships ({missingEvents.length})</span>
                    </div>
                    {expanded.missing ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent className="px-4 pb-4">
                    {missingEvents.length === 0 ? (
                      <p className="text-sm text-muted-foreground py-2">No events missing venues</p>
                    ) : (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Event Name</TableHead>
                            <TableHead>Date</TableHead>
                            <TableHead>Azure Venue</TableHead>
                            <TableHead>PostgreSQL Venue</TableHead>
                            <TableHead>Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {missingEvents.map(event => (
                            <TableRow key={event.id}>
                              <TableCell className="font-medium">{event.name}</TableCell>
                              <TableCell>{formatDate(event.date)}</TableCell>
                              <TableCell>{event.azure_venue_name}</TableCell>
                              <TableCell className="text-red-500">{event.postgres_venue_name || 'Missing'}</TableCell>
                              <TableCell>
                                <Button
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => openFixConfirmDialog(event)}
                                  disabled={fixingEvents[event.id]}
                                >
                                  {fixingEvents[event.id] ? (
                                    <>
                                      <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                                      Fixing...
                                    </>
                                  ) : (
                                    "Fix"
                                  )}
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    )}
                  </CollapsibleContent>
                </Collapsible>
                
                {/* Mismatched Venues section */}
                <Collapsible 
                  open={expanded.mismatched} 
                  onOpenChange={() => toggleExpanded('mismatched')}
                  className="border rounded-md"
                >
                  <CollapsibleTrigger className="flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors">
                    <div className="flex items-center">
                      <Calendar className="mr-2 h-4 w-4" />
                      <span>Events with Mismatched Venue ({mismatchedEvents.length})</span>
                    </div>
                    {expanded.mismatched ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent className="px-4 pb-4">
                    {mismatchedEvents.length === 0 ? (
                      <p className="text-sm text-muted-foreground py-2">No events with mismatched venues</p>
                    ) : (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Event Name</TableHead>
                            <TableHead>Date</TableHead>
                            <TableHead>Azure Venue</TableHead>
                            <TableHead>PostgreSQL Venue</TableHead>
                            <TableHead>Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {mismatchedEvents.map(event => (
                            <TableRow key={event.id}>
                              <TableCell className="font-medium">{event.name}</TableCell>
                              <TableCell>{formatDate(event.date)}</TableCell>
                              <TableCell>{event.azure_venue_name}</TableCell>
                              <TableCell className="text-yellow-500">{event.postgres_venue_name}</TableCell>
                              <TableCell>
                                <Button
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => openFixConfirmDialog(event)}
                                  disabled={fixingEvents[event.id]}
                                >
                                  {fixingEvents[event.id] ? (
                                    <>
                                      <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                                      Fixing...
                                    </>
                                  ) : (
                                    "Fix"
                                  )}
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    )}
                  </CollapsibleContent>
                </Collapsible>
              </div>
            </div>
          </>
        )}

      {/* Confirmation Dialog */}
      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Fix Event-Venue Relationship</DialogTitle>
            <DialogDescription>
              {selectedEvent && (
                <div className="space-y-4 py-2">
                  <p>Are you sure you want to fix the venue relationship for this event?</p>
                  
                  <div className="bg-muted p-3 rounded-md text-sm">
                    <p><strong>Event:</strong> {selectedEvent?.name}</p>
                    <p><strong>Date:</strong> {formatDate(selectedEvent?.date || '')}</p>
                    <p><strong>Current Venue:</strong> {selectedEvent?.postgres_venue_name || 'None'}</p>
                    <p><strong>Target Venue:</strong> {selectedEvent?.azure_venue_name}</p>
                  </div>
                  
                  <p>This will:</p>
                  <ul className="list-disc pl-5 space-y-1">
                    <li>Update the event to reference the correct venue from Azure</li>
                    <li>Create the venue in PostgreSQL if it doesn't exist</li>
                    <li>Update the event's venue_id to maintain data consistency</li>
                  </ul>
                </div>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setConfirmDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleFixEventVenue}>Fix</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </CardContent>
    </Card>
  );
}
